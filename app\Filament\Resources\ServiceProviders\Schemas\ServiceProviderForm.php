<?php

namespace App\Filament\Resources\ServiceProviders\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class ServiceProviderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('company_registration')
                    ->default(null),
                TextInput::make('tax_id')
                    ->default(null),
                TextInput::make('contact_person')
                    ->default(null),
                TextInput::make('email')
                    ->email()
                    ->default(null),
                TextInput::make('phone')
                    ->tel()
                    ->default(null),
                TextInput::make('mobile')
                    ->default(null),
                TextInput::make('website')
                    ->default(null),
                Textarea::make('address')
                    ->default(null)
                    ->columnSpanFull(),
                TextInput::make('city')
                    ->default(null),
                TextInput::make('state')
                    ->default(null),
                TextInput::make('postal_code')
                    ->default(null),
                TextInput::make('country')
                    ->required()
                    ->default('US'),
                Textarea::make('service_specialties')
                    ->default(null)
                    ->columnSpanFull(),
                Toggle::make('is_certified')
                    ->required(),
                Textarea::make('certifications')
                    ->default(null)
                    ->columnSpanFull(),
                DatePicker::make('certification_expiry'),
                Textarea::make('operating_hours')
                    ->default(null)
                    ->columnSpanFull(),
                Toggle::make('is_active')
                    ->required(),
                Textarea::make('notes')
                    ->default(null)
                    ->columnSpanFull(),
            ]);
    }
}
