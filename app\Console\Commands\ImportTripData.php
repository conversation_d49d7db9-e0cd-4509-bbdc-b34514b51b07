<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TripDataImportService;
use Illuminate\Support\Facades\Storage;

class ImportTripData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trips:import 
                            {file : The CSV file to import}
                            {--dry-run : Run without actually importing data}
                            {--skip-automation : Skip running automation after import}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import trip data from CSV file';

    private TripDataImportService $importService;

    public function __construct(TripDataImportService $importService)
    {
        parent::__construct();
        $this->importService = $importService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $filePath = $this->argument('file');
        $dryRun = $this->option('dry-run');
        $skipAutomation = $this->option('skip-automation');

        // Check if file exists
        if (!Storage::exists($filePath) && !file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Starting trip data import from: {$filePath}");
        
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No data will be imported");
        }

        try {
            // Read CSV file
            $data = $this->readCsvFile($filePath);
            
            if (empty($data)) {
                $this->error("No data found in file or file is empty");
                return 1;
            }

            $this->info("Found " . count($data) . " rows to process");

            // Show preview of first few rows
            $this->showDataPreview($data);

            // Confirm before proceeding
            if (!$dryRun && !$this->confirm('Do you want to proceed with the import?')) {
                $this->info('Import cancelled');
                return 0;
            }

            // Process import
            if (!$dryRun) {
                $results = $this->importService->importTripData($data);
                $this->displayResults($results);
            } else {
                $this->info("DRY RUN: Would process " . count($data) . " rows");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Read CSV file and return data array
     */
    private function readCsvFile(string $filePath): array
    {
        $data = [];
        $headers = [];

        if (Storage::exists($filePath)) {
            $content = Storage::get($filePath);
            $lines = str_getcsv($content, "\n");
        } else {
            $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        }

        foreach ($lines as $index => $line) {
            $row = str_getcsv($line);
            
            if ($index === 0) {
                $headers = $row;
                continue;
            }

            if (count($row) === count($headers)) {
                $data[] = array_combine($headers, $row);
            }
        }

        return $data;
    }

    /**
     * Show preview of data to be imported
     */
    private function showDataPreview(array $data): void
    {
        $this->info("\nData Preview (first 3 rows):");
        
        $previewData = array_slice($data, 0, 3);
        
        $headers = ['Field', 'Row 1', 'Row 2', 'Row 3'];
        $rows = [];

        $importantFields = ['Trip No', 'Driver Name', 'Client', 'Product', 'Loading Quantity', 'Route'];

        foreach ($importantFields as $field) {
            $row = [$field];
            for ($i = 0; $i < 3; $i++) {
                $row[] = $previewData[$i][$field] ?? 'N/A';
            }
            $rows[] = $row;
        }

        $this->table($headers, $rows);
    }

    /**
     * Display import results
     */
    private function displayResults(array $results): void
    {
        $this->info("\n=== Import Results ===");
        $this->info("Successfully imported: {$results['imported']} trips");
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to import: {$results['failed']} trips");
        }

        if (!empty($results['errors'])) {
            $this->error("\nErrors:");
            foreach ($results['errors'] as $error) {
                $this->line("  - {$error}");
            }
        }

        if (!empty($results['warnings'])) {
            $this->warn("\nWarnings:");
            foreach ($results['warnings'] as $warning) {
                $this->line("  - {$warning}");
            }
        }

        $this->info("\nImport completed!");
    }
}
