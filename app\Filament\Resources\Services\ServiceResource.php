<?php

namespace App\Filament\Resources\Services;

use App\Filament\Resources\Services\Pages\CreateService;
use App\Filament\Resources\Services\Pages\EditService;
use App\Filament\Resources\Services\Pages\ListServices;
use App\Models\Service;
use BackedEnum;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use UnitEnum;

class ServiceResource extends Resource
{
    protected static ?string $model = Service::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::Wrench;
    protected static string|UnitEnum|null $navigationGroup = 'Service Management';
    protected static ?int $navigationSort = 3;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Service Information')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Textarea::make('description')
                            ->rows(3),
                        Select::make('service_type_id')
                            ->relationship('serviceType', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Service Type'),
                        Select::make('service_category')
                            ->options([
                                'preventive' => 'Preventive',
                                'corrective' => 'Corrective',
                                'emergency' => 'Emergency',
                                'inspection' => 'Inspection',
                            ])
                            ->default('preventive')
                            ->required(),
                        Select::make('priority')
                            ->options([
                                'low' => 'Low',
                                'medium' => 'Medium',
                                'high' => 'High',
                                'critical' => 'Critical',
                            ])
                            ->default('medium')
                            ->required(),
                    ])
                    ->columns(2),

                Section::make('Assignment')
                    ->schema([
                        Select::make('vehicle_id')
                            ->relationship('vehicle', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Vehicle'),
                        TextInput::make('assigned_technician')
                            ->label('Assigned Technician')
                            ->maxLength(255),
                        Select::make('service_provider_id')
                            ->relationship('serviceProvider', 'name')
                            ->searchable()
                            ->preload()
                            ->label('Service Provider'),
                        Select::make('created_by')
                            ->relationship('createdBy', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->default(fn() => Auth::user()->id)
                            ->label('Created By'),
                    ])
                    ->columns(2),

                Section::make('Scheduling')
                    ->schema([
                        DateTimePicker::make('scheduled_start_datetime')
                            ->required()
                            ->label('Scheduled Start'),
                        DateTimePicker::make('scheduled_end_datetime')
                            ->required()
                            ->label('Scheduled End'),
                        DateTimePicker::make('actual_start_datetime')
                            ->label('Actual Start'),
                        DateTimePicker::make('actual_end_datetime')
                            ->label('Actual End'),
                    ])
                    ->columns(2),

                Section::make('Odometer & Financial')
                    ->schema([
                        TextInput::make('odometer_reading_start')
                            ->numeric()
                            ->label('Odometer Start (KM)'),
                        TextInput::make('odometer_reading_end')
                            ->numeric()
                            ->label('Odometer End (KM)'),
                        TextInput::make('total_cost')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->default(0)
                            ->required()
                            ->label('Total Cost'),
                        TextInput::make('paid_amount')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->default(0)
                            ->required()
                            ->label('Paid Amount'),
                    ])
                    ->columns(2),

                Section::make('Status & Documentation')
                    ->schema([
                        Select::make('status')
                            ->options([
                                'scheduled' => 'Scheduled',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                                'on_hold' => 'On Hold',
                            ])
                            ->default('scheduled')
                            ->required(),
                        Select::make('payment_status')
                            ->options([
                                'pending' => 'Pending',
                                'partial' => 'Partial',
                                'paid' => 'Paid',
                                'not_applicable' => 'Not Applicable',
                            ])
                            ->default('not_applicable')
                            ->required(),
                        Textarea::make('notes')
                            ->rows(4)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('vehicle.name')
                    ->searchable()
                    ->sortable()
                    ->label('Vehicle'),
                TextColumn::make('serviceType.name')
                    ->searchable()
                    ->label('Service Type'),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'scheduled' => 'gray',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        'on_hold' => 'info',
                    }),
                TextColumn::make('priority')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'low' => 'gray',
                        'medium' => 'info',
                        'high' => 'warning',
                        'critical' => 'danger',
                    }),
                TextColumn::make('scheduled_start_datetime')
                    ->dateTime()
                    ->sortable()
                    ->label('Scheduled Start'),
                TextColumn::make('total_cost')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'partial' => 'info',
                        'paid' => 'success',
                        'not_applicable' => 'gray',
                    }),
                TextColumn::make('assigned_technician')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('serviceProvider.name')
                    ->label('Service Provider')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                        'on_hold' => 'On Hold',
                    ]),
                SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                        'critical' => 'Critical',
                    ]),
                SelectFilter::make('service_category')
                    ->options([
                        'preventive' => 'Preventive',
                        'corrective' => 'Corrective',
                        'emergency' => 'Emergency',
                        'inspection' => 'Inspection',
                    ])
                    ->label('Category'),
                SelectFilter::make('payment_status')
                    ->options([
                        'pending' => 'Pending',
                        'partial' => 'Partial',
                        'paid' => 'Paid',
                        'not_applicable' => 'Not Applicable',
                    ]),
                Filter::make('overdue')
                    ->query(fn(Builder $query): Builder => $query->overdue())
                    ->label('Overdue Services'),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->defaultSort('scheduled_start_datetime', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListServices::route('/'),
            'create' => CreateService::route('/create'),
            'edit' => EditService::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}