<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\Vehicle;
use App\Models\Driver;
use App\Models\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class TripDataImportService
{
    private TripAutomationService $automationService;

    public function __construct(TripAutomationService $automationService)
    {
        $this->automationService = $automationService;
    }

    /**
     * Import trip data from array (e.g., from spreadsheet)
     */
    public function importTripData(array $data): array
    {
        $results = [
            'imported' => 0,
            'failed' => 0,
            'errors' => [],
            'warnings' => []
        ];

        DB::beginTransaction();

        try {
            foreach ($data as $index => $row) {
                $rowNumber = $index + 1;
                
                try {
                    $validatedData = $this->validateAndTransformRow($row, $rowNumber);
                    
                    if (!empty($validatedData['errors'])) {
                        $results['failed']++;
                        $results['errors'] = array_merge($results['errors'], $validatedData['errors']);
                        continue;
                    }

                    $trip = $this->createTripFromData($validatedData['data']);
                    
                    // Run automation if enabled
                    if ($trip->auto_generate_sobbies || $trip->auto_generate_reimbursements || $trip->auto_calculate_shortages) {
                        $this->automationService->processTrip($trip);
                    }

                    $results['imported']++;
                    
                    if (!empty($validatedData['warnings'])) {
                        $results['warnings'] = array_merge($results['warnings'], $validatedData['warnings']);
                    }

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Row {$rowNumber}: " . $e->getMessage();
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            $results['errors'][] = 'Import failed: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Validate and transform a single row of data
     */
    private function validateAndTransformRow(array $row, int $rowNumber): array
    {
        $errors = [];
        $warnings = [];
        $data = [];

        // Map spreadsheet columns to database fields
        $columnMapping = $this->getColumnMapping();

        // Transform row data using column mapping
        foreach ($columnMapping as $excelColumn => $dbField) {
            if (isset($row[$excelColumn])) {
                $data[$dbField] = $row[$excelColumn];
            }
        }

        // Validate required fields
        $validator = Validator::make($data, $this->getValidationRules());

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $errors[] = "Row {$rowNumber}: {$error}";
            }
        }

        // Find or create related entities
        try {
            $data = $this->resolveRelatedEntities($data, $rowNumber, $warnings);
        } catch (\Exception $e) {
            $errors[] = "Row {$rowNumber}: " . $e->getMessage();
        }

        // Transform dates
        $data = $this->transformDates($data);

        // Calculate derived fields
        $data = $this->calculateDerivedFields($data);

        return [
            'data' => $data,
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Get column mapping from spreadsheet to database
     */
    private function getColumnMapping(): array
    {
        return [
            'Trip No' => 'trip_no',
            'CD3 No' => 'cd3_no',
            'Manifest No' => 'manifest_no',
            'Fleet No' => 'fleet_no', // Will be used to find vehicle
            'Driver Name' => 'driver_name', // Will be used to find driver
            'Passport' => 'passport',
            'Truck Reg' => 'truck_reg',
            'Trailer Reg' => 'trailer_reg',
            'Product' => 'product',
            'Cargo Type' => 'cargo_type',
            'Loading Quantity' => 'loading_quantity',
            'Client' => 'client_name', // Will be used to find client
            'Route' => 'route',
            'Loading Point' => 'loading_point',
            'Trip Start Date' => 'trip_start_date',
            'Date Arrived at Loading' => 'date_arrived_at_loading',
            'Load Date' => 'load_date',
            'Date Arrived at Offloading' => 'date_arrived_at_offloading',
            'Date Offloaded' => 'date_offloaded',
            'Trip End Date' => 'trip_end_date',
            'Quantity Loaded' => 'quantity_loaded',
            'Quantity Offloaded' => 'quantity_offloaded',
            'Loading First Weight' => 'loading_first_weight',
            'Loading Second Weight' => 'loading_second_weight',
            'Offloading First Weight' => 'offloading_first_weight',
            'Offloading Second Weight' => 'offloading_second_weight',
            'Rate (USD)/mt' => 'rate_usd_per_mt',
            'USD Price Per Ton' => 'usd_price_per_ton',
            'CD3 Amount' => 'cd3_amount',
            'Upfront Payment' => 'upfront_payment',
            'Amount Paid' => 'amount_paid',
            'Invoicing Rate' => 'invoicing_rate',
            'Supplier Invoice No' => 'supplier_invoice_no',
            'D Note' => 'd_note',
            'Waybill Ref' => 'waybill_ref',
            'Invoice From' => 'invoice_from',
            'Invoice No for Upfront Pymt' => 'invoice_no_upfront_payment',
            'Invoice No' => 'invoice_no',
            'Invoice Date' => 'invoice_date',
            'Invoice Submission Date' => 'invoice_submission_date',
            'Credit Days' => 'credit_days',
            'Date Received' => 'date_received',
            'Remarks' => 'remarks',
        ];
    }

    /**
     * Get validation rules
     */
    private function getValidationRules(): array
    {
        return [
            'trip_no' => 'required|string|unique:trips,trip_no',
            'product' => 'required|string',
            'loading_quantity' => 'required|numeric|min:0',
            'route' => 'required|string',
            'loading_point' => 'required|string',
            'trip_start_date' => 'required|date',
            'driver_name' => 'required|string',
            'client_name' => 'required|string',
        ];
    }

    /**
     * Resolve related entities (vehicle, driver, client)
     */
    private function resolveRelatedEntities(array $data, int $rowNumber, array &$warnings): array
    {
        // Find vehicle by fleet number or truck registration
        if (isset($data['fleet_no']) || isset($data['truck_reg'])) {
            $vehicle = Vehicle::where('name', $data['fleet_no'] ?? '')
                            ->orWhere('plate_number', $data['truck_reg'] ?? '')
                            ->first();
            
            if ($vehicle) {
                $data['vehicle_id'] = $vehicle->id;
            } else {
                $warnings[] = "Row {$rowNumber}: Vehicle not found for fleet no '{$data['fleet_no']}' or truck reg '{$data['truck_reg']}'";
            }
        }

        // Find driver by name
        if (isset($data['driver_name'])) {
            $driver = Driver::where('name', 'like', '%' . $data['driver_name'] . '%')->first();
            
            if ($driver) {
                $data['driver_id'] = $driver->id;
            } else {
                throw new \Exception("Driver not found: {$data['driver_name']}");
            }
        }

        // Find client by name
        if (isset($data['client_name'])) {
            $client = Client::where('name', 'like', '%' . $data['client_name'] . '%')->first();
            
            if ($client) {
                $data['client_id'] = $client->id;
            } else {
                throw new \Exception("Client not found: {$data['client_name']}");
            }
        }

        // Remove temporary fields
        unset($data['fleet_no'], $data['driver_name'], $data['client_name']);

        return $data;
    }

    /**
     * Transform date fields
     */
    private function transformDates(array $data): array
    {
        $dateFields = [
            'trip_start_date', 'date_arrived_at_loading', 'load_date',
            'date_arrived_at_offloading', 'date_offloaded', 'trip_end_date',
            'invoice_date', 'invoice_submission_date', 'date_received'
        ];

        foreach ($dateFields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                try {
                    $data[$field] = Carbon::parse($data[$field])->toDateString();
                } catch (\Exception $e) {
                    unset($data[$field]); // Remove invalid dates
                }
            }
        }

        return $data;
    }

    /**
     * Calculate derived fields
     */
    private function calculateDerivedFields(array $data): array
    {
        // Set default values
        $data['status'] = $data['status'] ?? 'draft';
        $data['credit_days'] = $data['credit_days'] ?? 30;
        $data['auto_generate_sobbies'] = true;
        $data['auto_generate_reimbursements'] = true;
        $data['auto_calculate_shortages'] = true;
        $data['is_automated_calculations_enabled'] = true;
        $data['created_by'] = auth()->id() ?? 1;

        return $data;
    }

    /**
     * Create trip from validated data
     */
    private function createTripFromData(array $data): Trip
    {
        return Trip::create($data);
    }

    /**
     * Get import template structure
     */
    public function getImportTemplate(): array
    {
        return [
            'columns' => array_keys($this->getColumnMapping()),
            'sample_data' => [
                [
                    'Trip No' => 'TRP-2025-001',
                    'CD3 No' => 'CD3-001',
                    'Manifest No' => 'MAN-001',
                    'Fleet No' => 'TRUCK-001',
                    'Driver Name' => 'John Doe',
                    'Passport' => '********',
                    'Truck Reg' => 'ABC-123-XY',
                    'Trailer Reg' => 'TRL-456-ZW',
                    'Product' => 'Diesel',
                    'Cargo Type' => 'liquid',
                    'Loading Quantity' => '30000',
                    'Client' => 'ABC Company Ltd',
                    'Route' => 'Lagos to Abuja',
                    'Loading Point' => 'Apapa Terminal',
                    'Trip Start Date' => '2025-01-15',
                    'Rate (USD)/mt' => '850.00',
                    'Credit Days' => '30',
                ]
            ]
        ];
    }
}
