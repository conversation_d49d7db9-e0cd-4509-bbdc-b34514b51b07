<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trips', function (Blueprint $table) {
            $table->id();
            
            // Core Trip Identification
            $table->string('trip_no')->unique();
            $table->string('cd3_no')->nullable();
            $table->string('manifest_no')->nullable();
            
            // Vehicle & Personnel Relationships
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->foreignId('driver_id')->constrained()->cascadeOnDelete();
            $table->foreignId('client_id')->constrained()->cascadeOnDelete();
            $table->string('truck_reg')->nullable(); // Additional truck registration
            $table->string('trailer_reg')->nullable();
            $table->string('passport')->nullable(); // Driver passport for this trip
            
            // Cargo Information
            $table->string('product');
            $table->string('cargo_type');
            $table->decimal('loading_quantity', 10, 2);
            $table->string('route');
            $table->string('loading_point');
            
            // Date/Timeline Data
            $table->date('trip_start_date');
            $table->date('date_arrived_at_loading')->nullable();
            $table->date('load_date')->nullable();
            $table->date('date_arrived_at_offloading')->nullable();
            $table->date('date_offloaded')->nullable();
            $table->date('trip_end_date')->nullable();
            
            // Weight/Quantity Data
            $table->decimal('quantity_loaded', 10, 2)->nullable();
            $table->decimal('quantity_offloaded', 10, 2)->nullable();
            $table->decimal('quantity_diff', 10, 2)->nullable(); // Auto-calculated
            
            // Loading Weight Data
            $table->decimal('loading_first_weight', 10, 2)->nullable();
            $table->decimal('loading_second_weight', 10, 2)->nullable();
            $table->decimal('loading_net_weight', 10, 2)->nullable(); // Auto-calculated
            
            // Offloading Weight Data
            $table->decimal('offloading_first_weight', 10, 2)->nullable();
            $table->decimal('offloading_second_weight', 10, 2)->nullable();
            $table->decimal('offloading_net_weight', 10, 2)->nullable(); // Auto-calculated
            
            // Financial Data (USD)
            $table->decimal('rate_usd_per_mt', 8, 2)->nullable();
            $table->decimal('usd_price_per_ton', 8, 2)->nullable();
            $table->decimal('usd_invoice_value', 12, 2)->nullable(); // Auto-calculated
            $table->decimal('cd3_amount', 12, 2)->nullable();
            $table->decimal('shortage_deduction', 12, 2)->default(0); // Auto-calculated
            $table->decimal('upfront_payment', 12, 2)->default(0);
            $table->decimal('invoice_total', 12, 2)->nullable(); // Auto-calculated
            $table->decimal('balance_due', 12, 2)->nullable(); // Auto-calculated
            $table->decimal('amount_paid', 12, 2)->default(0);
            $table->decimal('invoicing_rate', 8, 2)->nullable();
            
            // Documentation Data
            $table->string('supplier_invoice_no')->nullable();
            $table->string('d_note')->nullable();
            $table->string('waybill_ref')->nullable();
            $table->string('invoice_from')->nullable();
            $table->string('invoice_no_upfront_payment')->nullable();
            $table->string('invoice_no')->nullable();
            $table->date('invoice_date')->nullable();
            $table->date('invoice_submission_date')->nullable();
            $table->integer('credit_days')->default(30);
            $table->date('due_date')->nullable(); // Auto-calculated
            $table->date('date_received')->nullable();
            $table->text('remarks')->nullable();
            
            // Status and Automation Flags
            $table->enum('status', ['draft', 'in_progress', 'completed', 'invoiced', 'paid', 'cancelled'])
                  ->default('draft');
            $table->boolean('auto_generate_sobbies')->default(true);
            $table->boolean('auto_generate_reimbursements')->default(true);
            $table->boolean('auto_calculate_shortages')->default(true);
            $table->boolean('is_automated_calculations_enabled')->default(true);
            
            // Audit Fields
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'trip_start_date']);
            $table->index(['client_id', 'trip_start_date']);
            $table->index(['driver_id', 'trip_start_date']);
            $table->index(['vehicle_id', 'trip_start_date']);
            $table->index('trip_no');
            $table->index('cd3_no');
            $table->index(['invoice_date', 'due_date']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trips');
    }
};
