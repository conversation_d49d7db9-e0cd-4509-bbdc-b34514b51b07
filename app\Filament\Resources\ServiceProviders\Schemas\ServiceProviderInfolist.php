<?php

namespace App\Filament\Resources\ServiceProviders\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ServiceProviderInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('name'),
                TextEntry::make('company_registration'),
                TextEntry::make('tax_id'),
                TextEntry::make('contact_person'),
                TextEntry::make('email'),
                TextEntry::make('phone'),
                TextEntry::make('mobile'),
                TextEntry::make('website'),
                TextEntry::make('city'),
                TextEntry::make('state'),
                TextEntry::make('postal_code'),
                TextEntry::make('country'),
                IconEntry::make('is_certified')
                    ->boolean(),
                TextEntry::make('certification_expiry')
                    ->date(),
                IconEntry::make('is_active')
                    ->boolean(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
