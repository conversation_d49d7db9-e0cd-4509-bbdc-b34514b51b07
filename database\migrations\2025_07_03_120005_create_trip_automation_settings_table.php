<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_automation_settings', function (Blueprint $table) {
            $table->id();
            
            // Setting Identification
            $table->string('setting_key')->unique();
            $table->string('setting_name');
            $table->text('description')->nullable();
            $table->string('category'); // 'sobbies', 'reimbursements', 'shortages', 'calculations'
            
            // Setting Value
            $table->text('setting_value'); // JSON or simple value
            $table->string('data_type'); // 'decimal', 'integer', 'boolean', 'string', 'json'
            $table->text('default_value')->nullable();
            
            // Validation Rules
            $table->text('validation_rules')->nullable(); // JSON validation rules
            $table->decimal('min_value', 15, 4)->nullable();
            $table->decimal('max_value', 15, 4)->nullable();
            
            // Status and Control
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system_setting')->default(false); // Cannot be deleted
            $table->integer('sort_order')->default(0);
            
            // Audit Fields
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Indexes
            $table->index(['category', 'is_active']);
            $table->index('setting_key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trip_automation_settings');
    }
};
