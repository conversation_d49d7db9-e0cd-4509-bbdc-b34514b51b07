<?php

namespace App\Filament\Resources\Trips\Tables;

use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;

class TripsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('trip_no')
                    ->label('Trip No')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                
                TextColumn::make('vehicle.name')
                    ->label('Vehicle')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('driver.name')
                    ->label('Driver')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('client.name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('product')
                    ->searchable()
                    ->limit(20),
                
                TextColumn::make('route')
                    ->searchable()
                    ->limit(30),
                
                TextColumn::make('trip_start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable(),
                
                TextColumn::make('loading_quantity')
                    ->label('Quantity (MT)')
                    ->numeric(2)
                    ->sortable(),
                
                TextColumn::make('usd_invoice_value')
                    ->label('Invoice Value')
                    ->money('USD')
                    ->sortable(),
                
                TextColumn::make('balance_due')
                    ->label('Balance Due')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($record) => $record->balance_due > 0 ? 'danger' : 'success'),
                
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'primary' => 'invoiced',
                        'success' => 'paid',
                        'danger' => 'cancelled',
                    ])
                    ->sortable(),
                
                IconColumn::make('is_overdue')
                    ->label('Overdue')
                    ->boolean()
                    ->trueIcon('heroicon-o-exclamation-triangle')
                    ->falseIcon('heroicon-o-check-circle')
                    ->trueColor('danger')
                    ->falseColor('success'),
                
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'invoiced' => 'Invoiced',
                        'paid' => 'Paid',
                        'cancelled' => 'Cancelled',
                    ])
                    ->multiple(),
                
                SelectFilter::make('vehicle_id')
                    ->label('Vehicle')
                    ->relationship('vehicle', 'name')
                    ->searchable()
                    ->preload(),
                
                SelectFilter::make('driver_id')
                    ->label('Driver')
                    ->relationship('driver', 'name')
                    ->searchable()
                    ->preload(),
                
                SelectFilter::make('client_id')
                    ->label('Client')
                    ->relationship('client', 'name')
                    ->searchable()
                    ->preload(),
                
                Filter::make('trip_date_range')
                    ->form([
                        DatePicker::make('trip_start_from')
                            ->label('Trip Start From'),
                        DatePicker::make('trip_start_until')
                            ->label('Trip Start Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['trip_start_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('trip_start_date', '>=', $date),
                            )
                            ->when(
                                $data['trip_start_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('trip_start_date', '<=', $date),
                            );
                    }),
                
                Filter::make('overdue')
                    ->label('Overdue Payments')
                    ->query(fn (Builder $query): Builder => $query->overdue()),
                
                Filter::make('has_shortages')
                    ->label('Has Shortages')
                    ->query(fn (Builder $query): Builder => $query->whereHas('shortages')),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make(),
                    
                    Action::make('generate_sobbies')
                        ->label('Generate Sobbies')
                        ->icon('heroicon-o-currency-dollar')
                        ->color('success')
                        ->visible(fn ($record) => $record->auto_generate_sobbies && $record->sobbies()->count() === 0)
                        ->action(function ($record) {
                            $sobbies = \App\Models\Sobby::generateForTrip($record);
                            foreach ($sobbies as $sobby) {
                                $sobby->save();
                            }
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Sobbies Generated')
                                ->body(count($sobbies) . ' sobbies generated successfully.')
                                ->success()
                                ->send();
                        }),
                    
                    Action::make('generate_reimbursements')
                        ->label('Generate Reimbursements')
                        ->icon('heroicon-o-receipt-refund')
                        ->color('warning')
                        ->visible(fn ($record) => $record->auto_generate_reimbursements && $record->reimbursements()->count() === 0)
                        ->action(function ($record) {
                            $reimbursements = \App\Models\Reimbursement::generateForTrip($record);
                            foreach ($reimbursements as $reimbursement) {
                                $reimbursement->save();
                            }
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Reimbursements Generated')
                                ->body(count($reimbursements) . ' reimbursements generated successfully.')
                                ->success()
                                ->send();
                        }),
                    
                    Action::make('check_shortages')
                        ->label('Check Shortages')
                        ->icon('heroicon-o-exclamation-triangle')
                        ->color('danger')
                        ->visible(fn ($record) => $record->auto_calculate_shortages)
                        ->action(function ($record) {
                            $shortages = \App\Models\Shortage::generateForTrip($record);
                            foreach ($shortages as $shortage) {
                                $shortage->save();
                            }
                            
                            if (count($shortages) > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Shortages Detected')
                                    ->body(count($shortages) . ' shortages detected and recorded.')
                                    ->warning()
                                    ->send();
                            } else {
                                \Filament\Notifications\Notification::make()
                                    ->title('No Shortages')
                                    ->body('No significant shortages detected.')
                                    ->success()
                                    ->send();
                            }
                        }),
                    
                    DeleteAction::make(),
                ])
            ])
            ->bulkActions([
                // Bulk actions can be added here
            ])
            ->defaultSort('trip_start_date', 'desc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
