<?php

namespace App\Filament\Resources\Trips\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Schema;

class TripForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Trip Identification')
                    ->description('Basic trip information and identification')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('trip_no')
                                    ->label('Trip Number')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->placeholder('TRP-2025-001'),
                                TextInput::make('cd3_no')
                                    ->label('CD3 Number')
                                    ->placeholder('CD3-001'),
                                TextInput::make('manifest_no')
                                    ->label('Manifest Number')
                                    ->placeholder('MAN-001'),
                            ]),
                    ]),

                Section::make('Vehicle & Personnel')
                    ->description('Assign vehicle, driver, and client for this trip')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('vehicle_id')
                                    ->label('Vehicle')
                                    ->relationship('vehicle', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $vehicle = \App\Models\Vehicle::find($state);
                                            if ($vehicle) {
                                                $set('truck_reg', $vehicle->plate_number);
                                            }
                                        }
                                    }),
                                Select::make('driver_id')
                                    ->label('Driver')
                                    ->relationship('driver', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $driver = \App\Models\Driver::find($state);
                                            if ($driver) {
                                                $set('passport', $driver->license_number);
                                            }
                                        }
                                    }),
                                Select::make('client_id')
                                    ->label('Client')
                                    ->relationship('client', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                TextInput::make('truck_reg')
                                    ->label('Truck Registration')
                                    ->placeholder('Auto-filled from vehicle'),
                                TextInput::make('trailer_reg')
                                    ->label('Trailer Registration'),
                                TextInput::make('passport')
                                    ->label('Driver Passport/License')
                                    ->placeholder('Auto-filled from driver'),
                            ]),
                    ]),

                Section::make('Cargo Information')
                    ->description('Details about the cargo being transported')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('product')
                                    ->required()
                                    ->placeholder('e.g., Diesel, Petrol, Crude Oil'),
                                Select::make('cargo_type')
                                    ->options([
                                        'liquid' => 'Liquid',
                                        'solid' => 'Solid',
                                        'gas' => 'Gas',
                                        'hazardous' => 'Hazardous',
                                        'general' => 'General Cargo',
                                    ])
                                    ->required(),
                                TextInput::make('loading_quantity')
                                    ->label('Loading Quantity (MT)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('route')
                                    ->required()
                                    ->placeholder('e.g., Lagos to Abuja'),
                                TextInput::make('loading_point')
                                    ->required()
                                    ->placeholder('e.g., Apapa Terminal'),
                            ]),
                    ]),

                Section::make('Trip Timeline')
                    ->description('Important dates throughout the trip')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                DatePicker::make('trip_start_date')
                                    ->required()
                                    ->default(now()),
                                DatePicker::make('date_arrived_at_loading')
                                    ->label('Arrived at Loading'),
                                DatePicker::make('load_date')
                                    ->label('Loading Date'),
                                DatePicker::make('date_arrived_at_offloading')
                                    ->label('Arrived at Offloading'),
                                DatePicker::make('date_offloaded')
                                    ->label('Offloading Date'),
                                DatePicker::make('trip_end_date')
                                    ->label('Trip End Date'),
                            ]),
                    ]),

                Section::make('Quantity & Weight Data')
                    ->description('Actual quantities and weight measurements')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('quantity_loaded')
                                    ->label('Quantity Loaded (MT)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateQuantityDifference($set, $get);
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('quantity_offloaded')
                                    ->label('Quantity Offloaded (MT)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateQuantityDifference($set, $get);
                                    }),
                                TextInput::make('quantity_diff')
                                    ->label('Difference (MT)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->disabled()
                                    ->dehydrated(),
                            ]),
                        Grid::make(3)
                            ->schema([
                                TextInput::make('loading_first_weight')
                                    ->label('Loading First Weight (KG)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateLoadingNetWeight($set, $get);
                                    }),
                                TextInput::make('loading_second_weight')
                                    ->label('Loading Second Weight (KG)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateLoadingNetWeight($set, $get);
                                    }),
                                TextInput::make('loading_net_weight')
                                    ->label('Loading Net Weight (KG)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->disabled()
                                    ->dehydrated(),
                            ]),
                        Grid::make(3)
                            ->schema([
                                TextInput::make('offloading_first_weight')
                                    ->label('Offloading First Weight (KG)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateOffloadingNetWeight($set, $get);
                                    }),
                                TextInput::make('offloading_second_weight')
                                    ->label('Offloading Second Weight (KG)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateOffloadingNetWeight($set, $get);
                                    }),
                                TextInput::make('offloading_net_weight')
                                    ->label('Offloading Net Weight (KG)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->disabled()
                                    ->dehydrated(),
                            ]),
                    ]),

                Section::make('Financial Data')
                    ->description('Rates, pricing, and payment information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('rate_usd_per_mt')
                                    ->label('Rate (USD/MT)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('usd_price_per_ton')
                                    ->label('USD Price Per Ton')
                                    ->numeric()
                                    ->step(0.01)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('usd_invoice_value')
                                    ->label('USD Invoice Value')
                                    ->numeric()
                                    ->step(0.01)
                                    ->disabled()
                                    ->dehydrated(),
                                TextInput::make('cd3_amount')
                                    ->label('CD3 Amount')
                                    ->numeric()
                                    ->step(0.01),
                                TextInput::make('shortage_deduction')
                                    ->label('Shortage Deduction')
                                    ->numeric()
                                    ->step(0.01)
                                    ->default(0)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('upfront_payment')
                                    ->label('Upfront Payment')
                                    ->numeric()
                                    ->step(0.01)
                                    ->default(0)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('invoice_total')
                                    ->label('Invoice Total')
                                    ->numeric()
                                    ->step(0.01)
                                    ->disabled()
                                    ->dehydrated(),
                                TextInput::make('balance_due')
                                    ->label('Balance Due')
                                    ->numeric()
                                    ->step(0.01)
                                    ->disabled()
                                    ->dehydrated(),
                                TextInput::make('amount_paid')
                                    ->label('Amount Paid')
                                    ->numeric()
                                    ->step(0.01)
                                    ->default(0)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateFinancials($state, $set, $get);
                                    }),
                                TextInput::make('invoicing_rate')
                                    ->label('Invoicing Rate')
                                    ->numeric()
                                    ->step(0.01),
                            ]),
                    ]),

                Section::make('Documentation')
                    ->description('Invoice and documentation details')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('supplier_invoice_no')
                                    ->label('Supplier Invoice No'),
                                TextInput::make('d_note')
                                    ->label('D Note'),
                                TextInput::make('waybill_ref')
                                    ->label('Waybill Reference'),
                                TextInput::make('invoice_from')
                                    ->label('Invoice From'),
                                TextInput::make('invoice_no_upfront_payment')
                                    ->label('Invoice No (Upfront Payment)'),
                                TextInput::make('invoice_no')
                                    ->label('Invoice Number'),
                                DatePicker::make('invoice_date')
                                    ->label('Invoice Date')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateDueDate($set, $get);
                                    }),
                                DatePicker::make('invoice_submission_date')
                                    ->label('Invoice Submission Date'),
                                TextInput::make('credit_days')
                                    ->label('Credit Days')
                                    ->numeric()
                                    ->default(30)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        self::calculateDueDate($set, $get);
                                    }),
                                DatePicker::make('due_date')
                                    ->label('Due Date')
                                    ->disabled()
                                    ->dehydrated(),
                                DatePicker::make('date_received')
                                    ->label('Date Received'),
                            ]),
                        Textarea::make('remarks')
                            ->label('Remarks')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Section::make('Status & Automation')
                    ->description('Trip status and automation settings')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('status')
                                    ->options([
                                        'draft' => 'Draft',
                                        'in_progress' => 'In Progress',
                                        'completed' => 'Completed',
                                        'invoiced' => 'Invoiced',
                                        'paid' => 'Paid',
                                        'cancelled' => 'Cancelled',
                                    ])
                                    ->default('draft')
                                    ->required(),
                            ]),
                        Grid::make(4)
                            ->schema([
                                Toggle::make('auto_generate_sobbies')
                                    ->label('Auto Generate Sobbies')
                                    ->default(true),
                                Toggle::make('auto_generate_reimbursements')
                                    ->label('Auto Generate Reimbursements')
                                    ->default(true),
                                Toggle::make('auto_calculate_shortages')
                                    ->label('Auto Calculate Shortages')
                                    ->default(true),
                                Toggle::make('is_automated_calculations_enabled')
                                    ->label('Enable Automated Calculations')
                                    ->default(true),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    // Helper methods for calculations
    private static function calculateQuantityDifference(callable $set, callable $get): void
    {
        $loaded = $get('quantity_loaded');
        $offloaded = $get('quantity_offloaded');

        if ($loaded && $offloaded) {
            $difference = round($loaded - $offloaded, 2);
            $set('quantity_diff', $difference);
        }
    }

    private static function calculateLoadingNetWeight(callable $set, callable $get): void
    {
        $first = $get('loading_first_weight');
        $second = $get('loading_second_weight');

        if ($first && $second) {
            $net = round(abs($first - $second), 2);
            $set('loading_net_weight', $net);
        }
    }

    private static function calculateOffloadingNetWeight(callable $set, callable $get): void
    {
        $first = $get('offloading_first_weight');
        $second = $get('offloading_second_weight');

        if ($first && $second) {
            $net = round(abs($first - $second), 2);
            $set('offloading_net_weight', $net);
        }
    }

    private static function calculateFinancials($state, callable $set, callable $get): void
    {
        $rate = $get('rate_usd_per_mt') ?? $get('usd_price_per_ton');
        $quantity = $get('quantity_loaded') ?? $get('loading_quantity');

        if ($rate && $quantity) {
            $invoiceValue = round($rate * $quantity, 2);
            $set('usd_invoice_value', $invoiceValue);

            $shortageDeduction = $get('shortage_deduction') ?? 0;
            $invoiceTotal = round($invoiceValue - $shortageDeduction, 2);
            $set('invoice_total', $invoiceTotal);

            $upfront = $get('upfront_payment') ?? 0;
            $paid = $get('amount_paid') ?? 0;
            $balance = round($invoiceTotal - $upfront - $paid, 2);
            $set('balance_due', $balance);
        }
    }

    private static function calculateDueDate(callable $set, callable $get): void
    {
        $invoiceDate = $get('invoice_date');
        $creditDays = $get('credit_days') ?? 30;

        if ($invoiceDate) {
            $dueDate = \Carbon\Carbon::parse($invoiceDate)->addDays($creditDays);
            $set('due_date', $dueDate->toDateString());
        }
    }
}
