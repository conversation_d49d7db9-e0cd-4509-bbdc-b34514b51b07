<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

class TripAutomationSetting extends Model
{
    protected $guarded = [];

    protected $casts = [
        'min_value' => 'decimal:4',
        'max_value' => 'decimal:4',
        'is_active' => 'boolean',
        'is_system_setting' => 'boolean',
    ];

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Static Methods for Getting Settings
    public static function getValue(string $key, $defaultValue = null)
    {
        $cacheKey = "trip_automation_setting_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $defaultValue) {
            $setting = self::where('setting_key', $key)
                          ->where('is_active', true)
                          ->first();
            
            if (!$setting) {
                return $defaultValue;
            }
            
            return self::castValue($setting->setting_value, $setting->data_type);
        });
    }

    public static function setValue(string $key, $value, int $userId = null): bool
    {
        $setting = self::where('setting_key', $key)->first();
        
        if (!$setting) {
            return false; // Setting doesn't exist
        }
        
        $setting->setting_value = $value;
        $setting->updated_by = $userId ?? auth()->id();
        
        $result = $setting->save();
        
        if ($result) {
            // Clear cache
            Cache::forget("trip_automation_setting_{$key}");
        }
        
        return $result;
    }

    public static function getByCategory(string $category): array
    {
        return self::where('category', $category)
                  ->where('is_active', true)
                  ->orderBy('sort_order')
                  ->orderBy('setting_name')
                  ->get()
                  ->map(function ($setting) {
                      return [
                          'key' => $setting->setting_key,
                          'name' => $setting->setting_name,
                          'value' => self::castValue($setting->setting_value, $setting->data_type),
                          'data_type' => $setting->data_type,
                          'description' => $setting->description,
                      ];
                  })
                  ->toArray();
    }

    private static function castValue($value, string $dataType)
    {
        switch ($dataType) {
            case 'decimal':
                return (float) $value;
            case 'integer':
                return (int) $value;
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'json':
                return json_decode($value, true);
            case 'string':
            default:
                return (string) $value;
        }
    }

    // Business Logic Methods
    public function updateValue($value, int $userId = null): bool
    {
        // Validate against min/max if applicable
        if ($this->data_type === 'decimal' || $this->data_type === 'integer') {
            $numericValue = (float) $value;
            
            if ($this->min_value !== null && $numericValue < $this->min_value) {
                return false;
            }
            
            if ($this->max_value !== null && $numericValue > $this->max_value) {
                return false;
            }
        }
        
        $this->setting_value = $value;
        $this->updated_by = $userId ?? auth()->id();
        
        $result = $this->save();
        
        if ($result) {
            // Clear cache
            Cache::forget("trip_automation_setting_{$this->setting_key}");
        }
        
        return $result;
    }

    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }

    public function deactivate(): bool
    {
        if ($this->is_system_setting) {
            return false; // Cannot deactivate system settings
        }
        
        $this->is_active = false;
        $result = $this->save();
        
        if ($result) {
            // Clear cache
            Cache::forget("trip_automation_setting_{$this->setting_key}");
        }
        
        return $result;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeSystemSettings($query)
    {
        return $query->where('is_system_setting', true);
    }

    public function scopeUserSettings($query)
    {
        return $query->where('is_system_setting', false);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('setting_name');
    }

    // Static method to seed default settings
    public static function seedDefaultSettings(): void
    {
        $defaultSettings = [
            // Sobby Settings
            [
                'setting_key' => 'fuel_allowance_rate_per_km',
                'setting_name' => 'Fuel Allowance Rate per KM',
                'description' => 'Rate in USD per kilometer for fuel allowance calculation',
                'category' => 'sobbies',
                'setting_value' => '0.50',
                'data_type' => 'decimal',
                'default_value' => '0.50',
                'min_value' => 0.10,
                'max_value' => 2.00,
                'is_system_setting' => true,
                'sort_order' => 1,
            ],
            [
                'setting_key' => 'meal_allowance_daily_rate',
                'setting_name' => 'Daily Meal Allowance',
                'description' => 'Daily meal allowance in USD',
                'category' => 'sobbies',
                'setting_value' => '25.00',
                'data_type' => 'decimal',
                'default_value' => '25.00',
                'min_value' => 10.00,
                'max_value' => 100.00,
                'is_system_setting' => true,
                'sort_order' => 2,
            ],
            [
                'setting_key' => 'accommodation_allowance_daily_rate',
                'setting_name' => 'Daily Accommodation Allowance',
                'description' => 'Daily accommodation allowance in USD',
                'category' => 'sobbies',
                'setting_value' => '40.00',
                'data_type' => 'decimal',
                'default_value' => '40.00',
                'min_value' => 20.00,
                'max_value' => 150.00,
                'is_system_setting' => true,
                'sort_order' => 3,
            ],
            [
                'setting_key' => 'default_trip_distance_km',
                'setting_name' => 'Default Trip Distance (KM)',
                'description' => 'Default distance in kilometers when route distance cannot be calculated',
                'category' => 'sobbies',
                'setting_value' => '500',
                'data_type' => 'integer',
                'default_value' => '500',
                'min_value' => 100,
                'max_value' => 2000,
                'is_system_setting' => true,
                'sort_order' => 4,
            ],
            
            // Reimbursement Settings
            [
                'setting_key' => 'standard_toll_amount',
                'setting_name' => 'Standard Toll Amount',
                'description' => 'Standard toll amount in USD for automatic reimbursement generation',
                'category' => 'reimbursements',
                'setting_value' => '50.00',
                'data_type' => 'decimal',
                'default_value' => '50.00',
                'min_value' => 0.00,
                'max_value' => 200.00,
                'is_system_setting' => true,
                'sort_order' => 1,
            ],
            [
                'setting_key' => 'communication_allowance',
                'setting_name' => 'Communication Allowance',
                'description' => 'Communication allowance in USD for extended trips',
                'category' => 'reimbursements',
                'setting_value' => '20.00',
                'data_type' => 'decimal',
                'default_value' => '20.00',
                'min_value' => 0.00,
                'max_value' => 100.00,
                'is_system_setting' => true,
                'sort_order' => 2,
            ],
            
            // Shortage Settings
            [
                'setting_key' => 'shortage_threshold_percentage',
                'setting_name' => 'Shortage Threshold Percentage',
                'description' => 'Percentage threshold above which shortages are automatically flagged',
                'category' => 'shortages',
                'setting_value' => '2.0',
                'data_type' => 'decimal',
                'default_value' => '2.0',
                'min_value' => 0.1,
                'max_value' => 10.0,
                'is_system_setting' => true,
                'sort_order' => 1,
            ],
            [
                'setting_key' => 'weight_shortage_threshold_percentage',
                'setting_name' => 'Weight Shortage Threshold Percentage',
                'description' => 'Weight difference percentage threshold for shortage detection',
                'category' => 'shortages',
                'setting_value' => '3.0',
                'data_type' => 'decimal',
                'default_value' => '3.0',
                'min_value' => 0.1,
                'max_value' => 15.0,
                'is_system_setting' => true,
                'sort_order' => 2,
            ],
            [
                'setting_key' => 'shortage_penalty_percentage',
                'setting_name' => 'Shortage Penalty Percentage',
                'description' => 'Additional penalty percentage applied to shortage value',
                'category' => 'shortages',
                'setting_value' => '10.0',
                'data_type' => 'decimal',
                'default_value' => '10.0',
                'min_value' => 0.0,
                'max_value' => 50.0,
                'is_system_setting' => true,
                'sort_order' => 3,
            ],
            
            // General Calculation Settings
            [
                'setting_key' => 'default_credit_days',
                'setting_name' => 'Default Credit Days',
                'description' => 'Default number of credit days for invoice due date calculation',
                'category' => 'calculations',
                'setting_value' => '30',
                'data_type' => 'integer',
                'default_value' => '30',
                'min_value' => 1,
                'max_value' => 90,
                'is_system_setting' => true,
                'sort_order' => 1,
            ],
        ];

        foreach ($defaultSettings as $setting) {
            self::updateOrCreate(
                ['setting_key' => $setting['setting_key']],
                array_merge($setting, [
                    'is_active' => true,
                    'created_by' => 1, // Assuming admin user ID is 1
                ])
            );
        }
    }
}
