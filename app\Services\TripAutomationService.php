<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\Sobby;
use App\Models\Reimbursement;
use App\Models\Shortage;
use App\Models\TripAutomationSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TripAutomationService
{
    /**
     * Process all automation for a trip
     */
    public function processTrip(Trip $trip): array
    {
        $results = [
            'sobbies' => [],
            'reimbursements' => [],
            'shortages' => [],
            'errors' => []
        ];

        DB::beginTransaction();

        try {
            // Generate sobbies if enabled
            if ($trip->auto_generate_sobbies) {
                $results['sobbies'] = $this->generateSobbies($trip);
            }

            // Generate reimbursements if enabled
            if ($trip->auto_generate_reimbursements) {
                $results['reimbursements'] = $this->generateReimbursements($trip);
            }

            // Check for shortages if enabled
            if ($trip->auto_calculate_shortages) {
                $results['shortages'] = $this->checkShortages($trip);
            }

            // Update trip calculations
            $this->updateTripCalculations($trip);

            DB::commit();

            Log::info('Trip automation completed successfully', [
                'trip_id' => $trip->id,
                'trip_no' => $trip->trip_no,
                'results' => $results
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            $results['errors'][] = $e->getMessage();
            
            Log::error('Trip automation failed', [
                'trip_id' => $trip->id,
                'trip_no' => $trip->trip_no,
                'error' => $e->getMessage()
            ]);
        }

        return $results;
    }

    /**
     * Generate sobbies for a trip
     */
    private function generateSobbies(Trip $trip): array
    {
        $sobbies = [];

        // Check if sobbies already exist
        if ($trip->sobbies()->count() > 0) {
            return $sobbies;
        }

        $generatedSobbies = Sobby::generateForTrip($trip);
        
        foreach ($generatedSobbies as $sobby) {
            $sobby->save();
            $sobbies[] = $sobby;
        }

        return $sobbies;
    }

    /**
     * Generate reimbursements for a trip
     */
    private function generateReimbursements(Trip $trip): array
    {
        $reimbursements = [];

        // Check if reimbursements already exist
        if ($trip->reimbursements()->count() > 0) {
            return $reimbursements;
        }

        $generatedReimbursements = Reimbursement::generateForTrip($trip);
        
        foreach ($generatedReimbursements as $reimbursement) {
            $reimbursement->save();
            $reimbursements[] = $reimbursement;
        }

        return $reimbursements;
    }

    /**
     * Check for shortages in a trip
     */
    private function checkShortages(Trip $trip): array
    {
        $shortages = [];

        $generatedShortages = Shortage::generateForTrip($trip);
        
        foreach ($generatedShortages as $shortage) {
            $shortage->save();
            $shortages[] = $shortage;
            
            // Update trip shortage deduction
            $this->updateTripShortageDeduction($trip);
        }

        return $shortages;
    }

    /**
     * Update trip calculations
     */
    private function updateTripCalculations(Trip $trip): void
    {
        // Force recalculation of computed fields
        $trip->refresh();
        
        // Update shortage deduction based on shortages
        $this->updateTripShortageDeduction($trip);
        
        // Update due date if invoice date is set
        if ($trip->invoice_date && $trip->credit_days) {
            $trip->due_date = $trip->invoice_date->addDays($trip->credit_days);
        }
        
        $trip->save();
    }

    /**
     * Update trip shortage deduction
     */
    private function updateTripShortageDeduction(Trip $trip): void
    {
        $totalDeduction = $trip->shortages()
            ->where('status', '!=', 'disputed')
            ->sum('total_deduction');
        
        $trip->shortage_deduction = $totalDeduction;
        $trip->save();
    }

    /**
     * Validate trip data for automation
     */
    public function validateTripForAutomation(Trip $trip): array
    {
        $errors = [];

        // Check required fields for sobbies
        if ($trip->auto_generate_sobbies) {
            if (!$trip->trip_start_date) {
                $errors[] = 'Trip start date is required for sobby generation';
            }
            if (!$trip->driver_id) {
                $errors[] = 'Driver is required for sobby generation';
            }
        }

        // Check required fields for reimbursements
        if ($trip->auto_generate_reimbursements) {
            if (!$trip->trip_start_date) {
                $errors[] = 'Trip start date is required for reimbursement generation';
            }
            if (!$trip->driver_id) {
                $errors[] = 'Driver is required for reimbursement generation';
            }
        }

        // Check required fields for shortage calculation
        if ($trip->auto_calculate_shortages) {
            if (!$trip->quantity_loaded && !$trip->loading_quantity) {
                $errors[] = 'Loading quantity is required for shortage calculation';
            }
            if (!$trip->quantity_offloaded) {
                $errors[] = 'Offloaded quantity is required for shortage calculation';
            }
        }

        return $errors;
    }

    /**
     * Get automation summary for a trip
     */
    public function getAutomationSummary(Trip $trip): array
    {
        return [
            'trip_id' => $trip->id,
            'trip_no' => $trip->trip_no,
            'automation_enabled' => [
                'sobbies' => $trip->auto_generate_sobbies,
                'reimbursements' => $trip->auto_generate_reimbursements,
                'shortages' => $trip->auto_calculate_shortages,
                'calculations' => $trip->is_automated_calculations_enabled,
            ],
            'generated_records' => [
                'sobbies_count' => $trip->sobbies()->count(),
                'reimbursements_count' => $trip->reimbursements()->count(),
                'shortages_count' => $trip->shortages()->count(),
            ],
            'financial_summary' => [
                'invoice_value' => $trip->usd_invoice_value,
                'shortage_deduction' => $trip->shortage_deduction,
                'invoice_total' => $trip->invoice_total,
                'balance_due' => $trip->balance_due,
                'is_overdue' => $trip->isOverdue(),
            ],
            'validation_errors' => $this->validateTripForAutomation($trip),
        ];
    }

    /**
     * Bulk process automation for multiple trips
     */
    public function bulkProcessTrips(array $tripIds): array
    {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($tripIds as $tripId) {
            try {
                $trip = Trip::findOrFail($tripId);
                $this->processTrip($trip);
                $results['processed']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Trip ID {$tripId}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Recalculate all financial data for a trip
     */
    public function recalculateFinancials(Trip $trip): void
    {
        // Update shortage deduction
        $this->updateTripShortageDeduction($trip);
        
        // Trigger model accessors to recalculate
        $trip->refresh();
        
        // Save to persist calculated values
        $trip->save();
    }
}
