<?php

namespace App\Filament\Resources\ServiceTypes;

use App\Filament\Resources\ServiceTypes\Pages\ManageServiceTypes;
use App\Models\ServiceType;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\TagsInput;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use UnitEnum;

class ServiceTypeResource extends Resource
{
    protected static ?string $model = ServiceType::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::Cog6Tooth;
    protected static string|UnitEnum|null $navigationGroup = 'Service Management';
    protected static ?int $navigationSort = 1;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255)
                    ->helperText('Unique code for this service type (e.g., OIL_CHANGE)'),
                Textarea::make('description')
                    ->rows(3),
                Select::make('category')
                    ->options([
                        'preventive' => 'Preventive',
                        'corrective' => 'Corrective',
                        'inspection' => 'Inspection',
                    ])
                    ->default('preventive')
                    ->required(),
                TextInput::make('default_interval_km')
                    ->numeric()
                    ->label('Default Interval (KM)')
                    ->helperText('Default service interval in kilometers'),
                TextInput::make('default_interval_months')
                    ->numeric()
                    ->label('Default Interval (Months)')
                    ->helperText('Default service interval in months'),
                TextInput::make('estimated_cost')
                    ->numeric()
                    ->prefix('$')
                    ->step(0.01)
                    ->label('Estimated Cost'),
                TagsInput::make('required_skills')
                    ->label('Required Skills')
                    ->helperText('Skills required for this service type'),
                TagsInput::make('required_tools')
                    ->label('Required Tools')
                    ->helperText('Tools required for this service type'),
                Textarea::make('safety_notes')
                    ->label('Safety Notes')
                    ->rows(3),
                Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),
            ])
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('code')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('category')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'preventive' => 'success',
                        'corrective' => 'warning',
                        'inspection' => 'info',
                    }),
                TextColumn::make('estimated_cost')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('default_interval_km')
                    ->label('Interval (KM)')
                    ->numeric()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('default_interval_months')
                    ->label('Interval (Months)')
                    ->numeric()
                    ->toggleable(isToggledHiddenByDefault: true),
                IconColumn::make('is_active')
                    ->boolean()
                    ->label('Active'),
                TextColumn::make('services_count')
                    ->counts('services')
                    ->label('Services')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('category')
                    ->options([
                        'preventive' => 'Preventive',
                        'corrective' => 'Corrective',
                        'inspection' => 'Inspection',
                    ]),
                SelectFilter::make('is_active')
                    ->options([
                        1 => 'Active',
                        0 => 'Inactive',
                    ])
                    ->label('Status'),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getPages(): array
    {
        return [
            'index' => ManageServiceTypes::route('/'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
