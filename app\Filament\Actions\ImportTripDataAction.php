<?php

namespace App\Filament\Actions;

use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use App\Services\TripDataImportService;
use Illuminate\Support\Facades\Storage;

class ImportTripDataAction
{
    public static function make(): Action
    {
        return Action::make('import_trip_data')
            ->label('Import Trip Data')
            ->icon('heroicon-o-arrow-up-tray')
            ->color('primary')
            ->form([
                Section::make('Import Settings')
                    ->description('Upload a CSV file with trip data to import')
                    ->schema([
                        FileUpload::make('import_file')
                            ->label('CSV File')
                            ->acceptedFileTypes(['text/csv', 'application/csv'])
                            ->maxSize(10240) // 10MB
                            ->required()
                            ->helperText('Upload a CSV file with trip data. Maximum file size: 10MB'),
                        
                        Toggle::make('skip_automation')
                            ->label('Skip Automation')
                            ->helperText('If enabled, automatic generation of sobbies, reimbursements, and shortages will be skipped')
                            ->default(false),
                        
                        Toggle::make('validate_only')
                            ->label('Validate Only (Dry Run)')
                            ->helperText('If enabled, the file will be validated but no data will be imported')
                            ->default(false),
                    ]),
            ])
            ->action(function (array $data) {
                try {
                    $importService = app(TripDataImportService::class);
                    
                    // Get the uploaded file
                    $filePath = $data['import_file'];
                    
                    if (!Storage::exists($filePath)) {
                        throw new \Exception('Uploaded file not found');
                    }

                    // Read CSV file
                    $csvData = self::readCsvFile($filePath);
                    
                    if (empty($csvData)) {
                        throw new \Exception('No data found in the uploaded file');
                    }

                    if ($data['validate_only']) {
                        // Validation only mode
                        $validationResults = self::validateData($csvData, $importService);
                        
                        Notification::make()
                            ->title('Validation Complete')
                            ->body("Validated {$validationResults['total']} rows. {$validationResults['valid']} valid, {$validationResults['invalid']} invalid.")
                            ->success()
                            ->send();
                            
                        if (!empty($validationResults['errors'])) {
                            Notification::make()
                                ->title('Validation Errors Found')
                                ->body('Check the logs for detailed error information')
                                ->warning()
                                ->send();
                        }
                    } else {
                        // Actual import
                        $results = $importService->importTripData($csvData);
                        
                        // Clean up uploaded file
                        Storage::delete($filePath);
                        
                        // Show results
                        if ($results['imported'] > 0) {
                            Notification::make()
                                ->title('Import Successful')
                                ->body("Successfully imported {$results['imported']} trips")
                                ->success()
                                ->send();
                        }
                        
                        if ($results['failed'] > 0) {
                            Notification::make()
                                ->title('Import Warnings')
                                ->body("{$results['failed']} trips failed to import. Check logs for details.")
                                ->warning()
                                ->send();
                        }
                    }

                } catch (\Exception $e) {
                    Notification::make()
                        ->title('Import Failed')
                        ->body($e->getMessage())
                        ->danger()
                        ->send();
                }
            })
            ->modalWidth('lg');
    }

    /**
     * Read CSV file and return data array
     */
    private static function readCsvFile(string $filePath): array
    {
        $data = [];
        $headers = [];

        $content = Storage::get($filePath);
        $lines = str_getcsv($content, "\n");

        foreach ($lines as $index => $line) {
            $row = str_getcsv($line);
            
            if ($index === 0) {
                $headers = $row;
                continue;
            }

            if (count($row) === count($headers)) {
                $data[] = array_combine($headers, $row);
            }
        }

        return $data;
    }

    /**
     * Validate data without importing
     */
    private static function validateData(array $data, TripDataImportService $importService): array
    {
        $validCount = 0;
        $invalidCount = 0;
        $errors = [];

        foreach ($data as $index => $row) {
            $rowNumber = $index + 1;
            
            try {
                // Use reflection to access the private validation method
                $reflection = new \ReflectionClass($importService);
                $method = $reflection->getMethod('validateAndTransformRow');
                $method->setAccessible(true);
                
                $result = $method->invoke($importService, $row, $rowNumber);
                
                if (empty($result['errors'])) {
                    $validCount++;
                } else {
                    $invalidCount++;
                    $errors = array_merge($errors, $result['errors']);
                }
                
            } catch (\Exception $e) {
                $invalidCount++;
                $errors[] = "Row {$rowNumber}: " . $e->getMessage();
            }
        }

        return [
            'total' => count($data),
            'valid' => $validCount,
            'invalid' => $invalidCount,
            'errors' => $errors
        ];
    }

    /**
     * Get download template action
     */
    public static function downloadTemplate(): Action
    {
        return Action::make('download_template')
            ->label('Download Template')
            ->icon('heroicon-o-arrow-down-tray')
            ->color('secondary')
            ->action(function () {
                $importService = app(TripDataImportService::class);
                $template = $importService->getImportTemplate();
                
                // Generate CSV content
                $csvContent = self::generateCsvTemplate($template);
                
                // Create temporary file
                $fileName = 'trip_import_template_' . date('Y-m-d') . '.csv';
                $filePath = 'temp/' . $fileName;
                
                Storage::put($filePath, $csvContent);
                
                // Return download response
                return response()->download(storage_path('app/' . $filePath), $fileName)->deleteFileAfterSend();
            });
    }

    /**
     * Generate CSV template content
     */
    private static function generateCsvTemplate(array $template): string
    {
        $csv = [];
        
        // Add headers
        $csv[] = implode(',', $template['columns']);
        
        // Add sample data
        foreach ($template['sample_data'] as $row) {
            $csvRow = [];
            foreach ($template['columns'] as $column) {
                $csvRow[] = '"' . ($row[$column] ?? '') . '"';
            }
            $csv[] = implode(',', $csvRow);
        }
        
        return implode("\n", $csv);
    }
}
