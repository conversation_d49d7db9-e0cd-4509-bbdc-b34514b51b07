<?php

namespace App\Filament\Resources\Trips\Pages;

use App\Filament\Resources\Trips\TripResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTrip extends CreateRecord
{
    protected static string $resource = TripResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        
        return $data;
    }

    protected function afterCreate(): void
    {
        $trip = $this->record;
        
        // Auto-generate related records if enabled
        if ($trip->auto_generate_sobbies) {
            $sobbies = \App\Models\Sobby::generateForTrip($trip);
            foreach ($sobbies as $sobby) {
                $sobby->save();
            }
        }
        
        if ($trip->auto_generate_reimbursements) {
            $reimbursements = \App\Models\Reimbursement::generateForTrip($trip);
            foreach ($reimbursements as $reimbursement) {
                $reimbursement->save();
            }
        }
        
        if ($trip->auto_calculate_shortages && $trip->quantity_loaded && $trip->quantity_offloaded) {
            $shortages = \App\Models\Shortage::generateForTrip($trip);
            foreach ($shortages as $shortage) {
                $shortage->save();
            }
        }
        
        // Show notification about auto-generated records
        $generatedCount = 0;
        $generatedCount += $trip->sobbies()->count();
        $generatedCount += $trip->reimbursements()->count();
        $generatedCount += $trip->shortages()->count();
        
        if ($generatedCount > 0) {
            \Filament\Notifications\Notification::make()
                ->title('Trip Created Successfully')
                ->body("Trip created with {$generatedCount} auto-generated related records.")
                ->success()
                ->send();
        }
    }
}
