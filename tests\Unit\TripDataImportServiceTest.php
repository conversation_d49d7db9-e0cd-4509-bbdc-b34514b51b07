<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Vehicle;
use App\Models\Driver;
use App\Models\Client;
use App\Models\User;
use App\Models\VehicleType;
use App\Services\TripDataImportService;
use App\Services\TripAutomationService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TripDataImportServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TripDataImportService $importService;
    protected User $user;
    protected Vehicle $vehicle;
    protected Driver $driver;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $automationService = new TripAutomationService();
        $this->importService = new TripDataImportService($automationService);
        
        // Create test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Create required related models
        $vehicleType = VehicleType::create([
            'type' => 'Truck',
            'description' => 'Test Truck Type',
        ]);

        $this->vehicle = Vehicle::create([
            'vehicle_type_id' => $vehicleType->id,
            'name' => 'TRUCK-001',
            'model' => 'Test Model',
            'plate_number' => 'ABC-123-XY',
            'engine_type' => 'Diesel',
            'engine_number' => 'ENG-TEST-001',
            'registration_date' => '2023-01-01',
            'color' => 'White',
            'document' => 'test_doc.pdf',
        ]);

        $this->driver = Driver::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+234-************',
            'gender' => 'male',
            'date_of_birth' => '1985-01-01',
            'joining_date' => '2023-01-01',
            'address' => 'Test Address',
            'license_number' => 'DL001234567',
            'license_issue_date' => '2020-01-01',
            'license_expiry_date' => '2025-01-01',
        ]);

        $this->client = Client::create([
            'name' => 'ABC Oil Company Ltd',
            'email' => '<EMAIL>',
            'phone' => '+234-************',
            'address' => 'Test Client Address',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'Nigeria',
        ]);
    }

    public function test_gets_import_template(): void
    {
        $template = $this->importService->getImportTemplate();

        $this->assertArrayHasKey('columns', $template);
        $this->assertArrayHasKey('sample_data', $template);
        $this->assertIsArray($template['columns']);
        $this->assertIsArray($template['sample_data']);
        
        // Check that important columns are present
        $this->assertContains('Trip No', $template['columns']);
        $this->assertContains('Driver Name', $template['columns']);
        $this->assertContains('Client', $template['columns']);
        $this->assertContains('Product', $template['columns']);
        $this->assertContains('Loading Quantity', $template['columns']);
    }

    public function test_imports_valid_trip_data(): void
    {
        $importData = [
            [
                'Trip No' => 'IMPORT-001',
                'CD3 No' => 'CD3-001',
                'Fleet No' => 'TRUCK-001',
                'Driver Name' => 'John Doe',
                'Client' => 'ABC Oil Company Ltd',
                'Product' => 'Diesel',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '30000',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '2025-01-15',
                'Rate (USD)/mt' => '850.00',
                'Credit Days' => '30',
            ]
        ];

        $results = $this->importService->importTripData($importData);

        $this->assertEquals(1, $results['imported']);
        $this->assertEquals(0, $results['failed']);
        $this->assertEmpty($results['errors']);

        // Verify trip was created in database
        $this->assertDatabaseHas('trips', [
            'trip_no' => 'IMPORT-001',
            'product' => 'Diesel',
            'loading_quantity' => 30000.00,
        ]);
    }

    public function test_handles_invalid_trip_data(): void
    {
        $importData = [
            [
                'Trip No' => '', // Missing required field
                'Driver Name' => 'Nonexistent Driver',
                'Client' => 'Nonexistent Client',
                'Product' => 'Diesel',
                'Loading Quantity' => 'invalid_number',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => 'invalid_date',
            ]
        ];

        $results = $this->importService->importTripData($importData);

        $this->assertEquals(0, $results['imported']);
        $this->assertEquals(1, $results['failed']);
        $this->assertNotEmpty($results['errors']);
    }

    public function test_resolves_related_entities_correctly(): void
    {
        $importData = [
            [
                'Trip No' => 'IMPORT-002',
                'Fleet No' => 'TRUCK-001', // Should find our test vehicle
                'Driver Name' => 'John Doe', // Should find our test driver
                'Client' => 'ABC Oil Company Ltd', // Should find our test client
                'Product' => 'Diesel',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '25000',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '2025-01-15',
                'Rate (USD)/mt' => '850.00',
            ]
        ];

        $results = $this->importService->importTripData($importData);

        $this->assertEquals(1, $results['imported']);
        $this->assertEquals(0, $results['failed']);

        // Verify relationships were resolved correctly
        $trip = \App\Models\Trip::where('trip_no', 'IMPORT-002')->first();
        $this->assertNotNull($trip);
        $this->assertEquals($this->vehicle->id, $trip->vehicle_id);
        $this->assertEquals($this->driver->id, $trip->driver_id);
        $this->assertEquals($this->client->id, $trip->client_id);
    }

    public function test_handles_missing_related_entities(): void
    {
        $importData = [
            [
                'Trip No' => 'IMPORT-003',
                'Fleet No' => 'NONEXISTENT-TRUCK',
                'Driver Name' => 'Nonexistent Driver',
                'Client' => 'Nonexistent Client',
                'Product' => 'Diesel',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '25000',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '2025-01-15',
            ]
        ];

        $results = $this->importService->importTripData($importData);

        $this->assertEquals(0, $results['imported']);
        $this->assertEquals(1, $results['failed']);
        $this->assertNotEmpty($results['errors']);
        
        // Should contain error messages about missing entities
        $errorString = implode(' ', $results['errors']);
        $this->assertStringContainsString('Driver not found', $errorString);
        $this->assertStringContainsString('Client not found', $errorString);
    }

    public function test_transforms_dates_correctly(): void
    {
        $importData = [
            [
                'Trip No' => 'IMPORT-004',
                'Fleet No' => 'TRUCK-001',
                'Driver Name' => 'John Doe',
                'Client' => 'ABC Oil Company Ltd',
                'Product' => 'Diesel',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '30000',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '15/01/2025', // Different date format
                'Invoice Date' => '2025-01-20',
                'Rate (USD)/mt' => '850.00',
            ]
        ];

        $results = $this->importService->importTripData($importData);

        $this->assertEquals(1, $results['imported']);
        
        $trip = \App\Models\Trip::where('trip_no', 'IMPORT-004')->first();
        $this->assertNotNull($trip);
        $this->assertNotNull($trip->trip_start_date);
        $this->assertNotNull($trip->invoice_date);
    }

    public function test_sets_default_automation_flags(): void
    {
        $importData = [
            [
                'Trip No' => 'IMPORT-005',
                'Fleet No' => 'TRUCK-001',
                'Driver Name' => 'John Doe',
                'Client' => 'ABC Oil Company Ltd',
                'Product' => 'Diesel',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '30000',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '2025-01-15',
                'Rate (USD)/mt' => '850.00',
            ]
        ];

        $results = $this->importService->importTripData($importData);

        $this->assertEquals(1, $results['imported']);
        
        $trip = \App\Models\Trip::where('trip_no', 'IMPORT-005')->first();
        $this->assertNotNull($trip);
        $this->assertTrue($trip->auto_generate_sobbies);
        $this->assertTrue($trip->auto_generate_reimbursements);
        $this->assertTrue($trip->auto_calculate_shortages);
        $this->assertTrue($trip->is_automated_calculations_enabled);
    }

    public function test_handles_duplicate_trip_numbers(): void
    {
        // First import
        $importData1 = [
            [
                'Trip No' => 'DUPLICATE-001',
                'Fleet No' => 'TRUCK-001',
                'Driver Name' => 'John Doe',
                'Client' => 'ABC Oil Company Ltd',
                'Product' => 'Diesel',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '30000',
                'Route' => 'Lagos to Abuja',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '2025-01-15',
            ]
        ];

        $results1 = $this->importService->importTripData($importData1);
        $this->assertEquals(1, $results1['imported']);

        // Second import with same trip number
        $importData2 = [
            [
                'Trip No' => 'DUPLICATE-001', // Same trip number
                'Fleet No' => 'TRUCK-001',
                'Driver Name' => 'John Doe',
                'Client' => 'ABC Oil Company Ltd',
                'Product' => 'Petrol',
                'Cargo Type' => 'liquid',
                'Loading Quantity' => '25000',
                'Route' => 'Lagos to Kano',
                'Loading Point' => 'Apapa Terminal',
                'Trip Start Date' => '2025-01-16',
            ]
        ];

        $results2 = $this->importService->importTripData($importData2);
        $this->assertEquals(0, $results2['imported']);
        $this->assertEquals(1, $results2['failed']);
        $this->assertNotEmpty($results2['errors']);
    }
}
