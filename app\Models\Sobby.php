<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Sobby extends Model
{
    protected $guarded = [];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'amount_local_currency' => 'decimal:2',
        'trip_distance_km' => 'decimal:2',
        'rate_per_km' => 'decimal:4',
        'daily_allowance_rate' => 'decimal:2',
        'issue_date' => 'date',
        'payment_date' => 'date',
        'auto_generated' => 'boolean',
        'supporting_documents' => 'array',
        'calculation_details' => 'array',
    ];

    // Relationships
    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Automated Calculations
    protected function amountLocalCurrency(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateLocalCurrencyAmount(),
        );
    }

    private function calculateLocalCurrencyAmount(): ?float
    {
        if ($this->amount && $this->exchange_rate) {
            return round($this->amount * $this->exchange_rate, 2);
        }
        return null;
    }

    // Static Methods for Auto-Generation
    public static function generateForTrip(Trip $trip): array
    {
        $sobbies = [];
        
        if (!$trip->auto_generate_sobbies) {
            return $sobbies;
        }

        // Generate fuel allowance
        $fuelSobby = self::generateFuelAllowance($trip);
        if ($fuelSobby) {
            $sobbies[] = $fuelSobby;
        }

        // Generate meal allowance
        $mealSobby = self::generateMealAllowance($trip);
        if ($mealSobby) {
            $sobbies[] = $mealSobby;
        }

        // Generate accommodation allowance if trip duration > 1 day
        if ($trip->getTripDurationDays() > 1) {
            $accommodationSobby = self::generateAccommodationAllowance($trip);
            if ($accommodationSobby) {
                $sobbies[] = $accommodationSobby;
            }
        }

        return $sobbies;
    }

    private static function generateFuelAllowance(Trip $trip): ?self
    {
        $ratePerKm = TripAutomationSetting::getValue('fuel_allowance_rate_per_km', 0.50);
        $estimatedDistance = TripAutomationSetting::getValue('default_trip_distance_km', 500);
        
        // Try to calculate actual distance from route if available
        $distance = self::calculateTripDistance($trip->route) ?? $estimatedDistance;
        
        $amount = $distance * $ratePerKm;
        
        return new self([
            'trip_id' => $trip->id,
            'driver_id' => $trip->driver_id,
            'sobby_no' => self::generateSobbyNumber('FUEL'),
            'type' => 'fuel_allowance',
            'amount' => $amount,
            'trip_distance_km' => $distance,
            'rate_per_km' => $ratePerKm,
            'issue_date' => $trip->trip_start_date,
            'status' => 'pending',
            'auto_generated' => true,
            'calculation_details' => [
                'distance_km' => $distance,
                'rate_per_km' => $ratePerKm,
                'calculation' => "Distance ({$distance} km) × Rate ({$ratePerKm} USD/km)"
            ],
            'description' => "Auto-generated fuel allowance for trip {$trip->trip_no}",
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    private static function generateMealAllowance(Trip $trip): ?self
    {
        $dailyRate = TripAutomationSetting::getValue('meal_allowance_daily_rate', 25.00);
        $duration = $trip->getTripDurationDays() ?? 1;
        
        $amount = $duration * $dailyRate;
        
        return new self([
            'trip_id' => $trip->id,
            'driver_id' => $trip->driver_id,
            'sobby_no' => self::generateSobbyNumber('MEAL'),
            'type' => 'meal_allowance',
            'amount' => $amount,
            'trip_duration_days' => $duration,
            'daily_allowance_rate' => $dailyRate,
            'issue_date' => $trip->trip_start_date,
            'status' => 'pending',
            'auto_generated' => true,
            'calculation_details' => [
                'duration_days' => $duration,
                'daily_rate' => $dailyRate,
                'calculation' => "Duration ({$duration} days) × Daily Rate ({$dailyRate} USD/day)"
            ],
            'description' => "Auto-generated meal allowance for trip {$trip->trip_no}",
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    private static function generateAccommodationAllowance(Trip $trip): ?self
    {
        $dailyRate = TripAutomationSetting::getValue('accommodation_allowance_daily_rate', 40.00);
        $duration = $trip->getTripDurationDays() - 1; // Exclude first day
        
        if ($duration <= 0) {
            return null;
        }
        
        $amount = $duration * $dailyRate;
        
        return new self([
            'trip_id' => $trip->id,
            'driver_id' => $trip->driver_id,
            'sobby_no' => self::generateSobbyNumber('ACCOM'),
            'type' => 'accommodation',
            'amount' => $amount,
            'trip_duration_days' => $duration,
            'daily_allowance_rate' => $dailyRate,
            'issue_date' => $trip->trip_start_date,
            'status' => 'pending',
            'auto_generated' => true,
            'calculation_details' => [
                'duration_days' => $duration,
                'daily_rate' => $dailyRate,
                'calculation' => "Duration ({$duration} nights) × Daily Rate ({$dailyRate} USD/night)"
            ],
            'description' => "Auto-generated accommodation allowance for trip {$trip->trip_no}",
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    private static function calculateTripDistance(string $route): ?float
    {
        // This could be enhanced to integrate with mapping APIs
        // For now, return null to use default distance
        return null;
    }

    private static function generateSobbyNumber(string $type): string
    {
        $prefix = 'SOB-' . $type . '-';
        $date = now()->format('Ymd');
        $sequence = self::where('sobby_no', 'like', $prefix . $date . '%')->count() + 1;
        
        return $prefix . $date . '-' . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeAutoGenerated($query)
    {
        return $query->where('auto_generated', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByDriver($query, int $driverId)
    {
        return $query->where('driver_id', $driverId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('issue_date', [$startDate, $endDate]);
    }
}
