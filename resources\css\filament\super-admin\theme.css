@import "../../../../vendor/filament/filament/resources/css/theme.css";

@source '../../../../app/Filament/**/*';
@source '../../../../resources/views/filament/**/*';

@layer components {
    .fi-sidebar {
        @apply bg-white;
    }

    .fi-sidebar-nav::-webkit-scrollbar {
        @apply hidden;
    }
    .fi-sidebar-nav {
        @apply overflow-y-auto overflow-x-hidden;
    }

    .fi-sidebar-item.fi-active > .fi-sidebar-item-btn {
        @apply bg-primary-600;
    }

    .fi-sidebar-item.fi-active > .fi-sidebar-item-btn > .fi-icon {
        @apply text-white;
    }

    .fi-sidebar-item.fi-active:hover > .fi-sidebar-item-btn > .fi-icon {
        @apply text-primary-500;
    }

    .fi-sidebar-item.fi-active > .fi-sidebar-item-btn > .fi-sidebar-item-label {
        @apply text-white hover:text-white;
    }

    .fi-sidebar-item.fi-active:hover
        > .fi-sidebar-item-btn
        > .fi-sidebar-item-label {
        @apply text-primary-500;
    }
}
