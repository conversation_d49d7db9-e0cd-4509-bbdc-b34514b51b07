<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Shortage extends Model
{
    protected $guarded = [];

    protected $casts = [
        'expected_quantity' => 'decimal:2',
        'actual_quantity' => 'decimal:2',
        'shortage_quantity' => 'decimal:2',
        'shortage_percentage' => 'decimal:2',
        'expected_weight' => 'decimal:2',
        'actual_weight' => 'decimal:2',
        'weight_difference' => 'decimal:2',
        'unit_rate' => 'decimal:2',
        'shortage_value' => 'decimal:2',
        'deduction_amount' => 'decimal:2',
        'penalty_percentage' => 'decimal:2',
        'penalty_amount' => 'decimal:2',
        'total_deduction' => 'decimal:2',
        'tolerance_threshold' => 'decimal:2',
        'identified_date' => 'date',
        'investigation_date' => 'date',
        'resolution_date' => 'date',
        'client_notification_date' => 'date',
        'client_notified' => 'boolean',
        'client_accepts_deduction' => 'boolean',
        'auto_generated' => 'boolean',
        'evidence_documents' => 'array',
        'weight_tickets' => 'array',
        'calculation_details' => 'array',
    ];

    // Relationships
    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function identifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'identified_by');
    }

    public function investigatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'investigated_by');
    }

    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    // Automated Calculations
    protected function shortageQuantity(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateShortageQuantity(),
        );
    }

    protected function shortagePercentage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateShortagePercentage(),
        );
    }

    protected function weightDifference(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateWeightDifference(),
        );
    }

    protected function shortageValue(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateShortageValue(),
        );
    }

    protected function penaltyAmount(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculatePenaltyAmount(),
        );
    }

    protected function totalDeduction(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateTotalDeduction(),
        );
    }

    // Calculation Methods
    private function calculateShortageQuantity(): ?float
    {
        if ($this->expected_quantity && $this->actual_quantity) {
            return round($this->expected_quantity - $this->actual_quantity, 2);
        }
        return null;
    }

    private function calculateShortagePercentage(): ?float
    {
        $shortage = $this->shortage_quantity ?? $this->calculateShortageQuantity();
        if ($shortage && $this->expected_quantity && $this->expected_quantity > 0) {
            return round(($shortage / $this->expected_quantity) * 100, 2);
        }
        return null;
    }

    private function calculateWeightDifference(): ?float
    {
        if ($this->expected_weight && $this->actual_weight) {
            return round($this->expected_weight - $this->actual_weight, 2);
        }
        return null;
    }

    private function calculateShortageValue(): ?float
    {
        $shortage = $this->shortage_quantity ?? $this->calculateShortageQuantity();
        if ($shortage && $this->unit_rate) {
            return round($shortage * $this->unit_rate, 2);
        }
        return null;
    }

    private function calculatePenaltyAmount(): ?float
    {
        $shortageValue = $this->shortage_value ?? $this->calculateShortageValue();
        if ($shortageValue && $this->penalty_percentage) {
            return round($shortageValue * ($this->penalty_percentage / 100), 2);
        }
        return null;
    }

    private function calculateTotalDeduction(): ?float
    {
        $shortageValue = $this->shortage_value ?? $this->calculateShortageValue();
        $penaltyAmount = $this->penalty_amount ?? $this->calculatePenaltyAmount();
        
        if ($shortageValue) {
            return round($shortageValue + ($penaltyAmount ?? 0), 2);
        }
        return null;
    }

    // Static Methods for Auto-Generation
    public static function generateForTrip(Trip $trip): array
    {
        $shortages = [];
        
        if (!$trip->auto_calculate_shortages) {
            return $shortages;
        }

        // Check for quantity shortage
        $quantityShortage = self::checkQuantityShortage($trip);
        if ($quantityShortage) {
            $shortages[] = $quantityShortage;
        }

        // Check for weight shortage
        $weightShortage = self::checkWeightShortage($trip);
        if ($weightShortage) {
            $shortages[] = $weightShortage;
        }

        return $shortages;
    }

    private static function checkQuantityShortage(Trip $trip): ?self
    {
        $loaded = $trip->quantity_loaded ?? $trip->loading_quantity;
        $offloaded = $trip->quantity_offloaded;
        
        if (!$loaded || !$offloaded) {
            return null;
        }

        $shortage = $loaded - $offloaded;
        $shortagePercentage = ($shortage / $loaded) * 100;
        
        $threshold = TripAutomationSetting::getValue('shortage_threshold_percentage', 2.0);
        
        if ($shortagePercentage <= $threshold) {
            return null; // Within acceptable tolerance
        }

        $rate = $trip->rate_usd_per_mt ?? $trip->usd_price_per_ton ?? 0;
        $penaltyPercentage = TripAutomationSetting::getValue('shortage_penalty_percentage', 10.0);

        return new self([
            'trip_id' => $trip->id,
            'client_id' => $trip->client_id,
            'shortage_no' => self::generateShortageNumber('QTY'),
            'type' => 'quantity_shortage',
            'expected_quantity' => $loaded,
            'actual_quantity' => $offloaded,
            'unit_rate' => $rate,
            'penalty_percentage' => $penaltyPercentage,
            'tolerance_threshold' => $threshold,
            'identified_date' => now()->toDateString(),
            'status' => 'identified',
            'description' => "Quantity shortage identified for trip {$trip->trip_no}",
            'auto_generated' => true,
            'calculation_details' => [
                'loaded_quantity' => $loaded,
                'offloaded_quantity' => $offloaded,
                'shortage_quantity' => $shortage,
                'shortage_percentage' => $shortagePercentage,
                'threshold_percentage' => $threshold,
                'unit_rate' => $rate,
                'penalty_percentage' => $penaltyPercentage
            ],
            'identified_by' => auth()->id() ?? 1,
        ]);
    }

    private static function checkWeightShortage(Trip $trip): ?self
    {
        $loadingWeight = $trip->loading_net_weight;
        $offloadingWeight = $trip->offloading_net_weight;
        
        if (!$loadingWeight || !$offloadingWeight) {
            return null;
        }

        $weightDiff = $loadingWeight - $offloadingWeight;
        $weightDiffPercentage = ($weightDiff / $loadingWeight) * 100;
        
        $threshold = TripAutomationSetting::getValue('weight_shortage_threshold_percentage', 3.0);
        
        if ($weightDiffPercentage <= $threshold) {
            return null; // Within acceptable tolerance
        }

        $rate = $trip->rate_usd_per_mt ?? $trip->usd_price_per_ton ?? 0;

        return new self([
            'trip_id' => $trip->id,
            'client_id' => $trip->client_id,
            'shortage_no' => self::generateShortageNumber('WGT'),
            'type' => 'weight_shortage',
            'expected_weight' => $loadingWeight,
            'actual_weight' => $offloadingWeight,
            'unit_rate' => $rate,
            'tolerance_threshold' => $threshold,
            'identified_date' => now()->toDateString(),
            'status' => 'identified',
            'description' => "Weight shortage identified for trip {$trip->trip_no}",
            'auto_generated' => true,
            'calculation_details' => [
                'loading_weight' => $loadingWeight,
                'offloading_weight' => $offloadingWeight,
                'weight_difference' => $weightDiff,
                'weight_diff_percentage' => $weightDiffPercentage,
                'threshold_percentage' => $threshold,
                'unit_rate' => $rate
            ],
            'identified_by' => auth()->id() ?? 1,
        ]);
    }

    private static function generateShortageNumber(string $type): string
    {
        $prefix = 'SHORT-' . $type . '-';
        $date = now()->format('Ymd');
        $sequence = self::where('shortage_no', 'like', $prefix . $date . '%')->count() + 1;
        
        return $prefix . $date . '-' . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    // Business Logic Methods
    public function investigate(string $cause = null, string $responsibility = null, string $notes = null): bool
    {
        $this->status = 'investigating';
        $this->investigation_date = now();
        $this->investigated_by = auth()->id();
        
        if ($cause) {
            $this->cause = $cause;
        }
        
        if ($responsibility) {
            $this->responsibility = $responsibility;
        }
        
        if ($notes) {
            $this->investigation_notes = $notes;
        }
        
        return $this->save();
    }

    public function resolve(string $resolutionNotes = null): bool
    {
        $this->status = 'resolved';
        $this->resolution_date = now();
        $this->resolved_by = auth()->id();
        
        if ($resolutionNotes) {
            $this->resolution_notes = $resolutionNotes;
        }
        
        return $this->save();
    }

    public function notifyClient(): bool
    {
        $this->client_notified = true;
        $this->client_notification_date = now();
        
        return $this->save();
    }

    // Scopes
    public function scopeIdentified($query)
    {
        return $query->where('status', 'identified');
    }

    public function scopeInvestigating($query)
    {
        return $query->where('status', 'investigating');
    }

    public function scopeDisputed($query)
    {
        return $query->where('status', 'disputed');
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    public function scopeAutoGenerated($query)
    {
        return $query->where('auto_generated', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByClient($query, int $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    public function scopeSignificant($query, float $threshold = 2.0)
    {
        return $query->where('shortage_percentage', '>', $threshold);
    }
}
