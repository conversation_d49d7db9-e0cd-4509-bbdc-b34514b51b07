<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Reimbursement extends Model
{
    protected $guarded = [];

    protected $casts = [
        'amount_claimed' => 'decimal:2',
        'amount_approved' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'amount_local_currency' => 'decimal:2',
        'expense_date' => 'date',
        'submission_date' => 'date',
        'review_date' => 'date',
        'approval_date' => 'date',
        'payment_date' => 'date',
        'auto_generated' => 'boolean',
        'receipt_documents' => 'array',
        'supporting_documents' => 'array',
        'auto_generation_rules' => 'array',
    ];

    // Relationships
    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function submittedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by');
    }

    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Automated Calculations
    protected function amountLocalCurrency(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->calculateLocalCurrencyAmount(),
        );
    }

    private function calculateLocalCurrencyAmount(): ?float
    {
        $amount = $this->amount_approved ?? $this->amount_claimed;
        if ($amount && $this->exchange_rate) {
            return round($amount * $this->exchange_rate, 2);
        }
        return null;
    }

    // Static Methods for Auto-Generation
    public static function generateForTrip(Trip $trip): array
    {
        $reimbursements = [];
        
        if (!$trip->auto_generate_reimbursements) {
            return $reimbursements;
        }

        // Generate standard trip-based reimbursements
        $standardReimbursements = self::generateStandardReimbursements($trip);
        $reimbursements = array_merge($reimbursements, $standardReimbursements);

        return $reimbursements;
    }

    private static function generateStandardReimbursements(Trip $trip): array
    {
        $reimbursements = [];
        
        // Generate toll reimbursement
        $tollAmount = TripAutomationSetting::getValue('standard_toll_amount', 50.00);
        if ($tollAmount > 0) {
            $reimbursements[] = new self([
                'trip_id' => $trip->id,
                'driver_id' => $trip->driver_id,
                'reimbursement_no' => self::generateReimbursementNumber('TOLL'),
                'category' => 'tolls',
                'amount_claimed' => $tollAmount,
                'expense_date' => $trip->trip_start_date,
                'description' => "Standard toll charges for route: {$trip->route}",
                'location' => $trip->route,
                'submission_date' => now()->toDateString(),
                'status' => 'submitted',
                'auto_generated' => true,
                'auto_generation_rules' => [
                    'rule_type' => 'standard_toll',
                    'amount' => $tollAmount,
                    'route' => $trip->route
                ],
                'submitted_by' => auth()->id() ?? 1,
            ]);
        }

        // Generate communication reimbursement for long trips
        if ($trip->getTripDurationDays() > 2) {
            $commAmount = TripAutomationSetting::getValue('communication_allowance', 20.00);
            if ($commAmount > 0) {
                $reimbursements[] = new self([
                    'trip_id' => $trip->id,
                    'driver_id' => $trip->driver_id,
                    'reimbursement_no' => self::generateReimbursementNumber('COMM'),
                    'category' => 'communication',
                    'amount_claimed' => $commAmount,
                    'expense_date' => $trip->trip_start_date,
                    'description' => "Communication allowance for extended trip duration",
                    'submission_date' => now()->toDateString(),
                    'status' => 'submitted',
                    'auto_generated' => true,
                    'auto_generation_rules' => [
                        'rule_type' => 'communication_allowance',
                        'amount' => $commAmount,
                        'trip_duration' => $trip->getTripDurationDays()
                    ],
                    'submitted_by' => auth()->id() ?? 1,
                ]);
            }
        }

        return $reimbursements;
    }

    private static function generateReimbursementNumber(string $category): string
    {
        $prefix = 'REIMB-' . strtoupper($category) . '-';
        $date = now()->format('Ymd');
        $sequence = self::where('reimbursement_no', 'like', $prefix . $date . '%')->count() + 1;
        
        return $prefix . $date . '-' . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    // Business Logic Methods
    public function approve(float $approvedAmount = null, string $notes = null): bool
    {
        $this->amount_approved = $approvedAmount ?? $this->amount_claimed;
        $this->status = 'approved';
        $this->approval_date = now();
        $this->approved_by = auth()->id();
        
        if ($notes) {
            $this->reviewer_notes = $notes;
        }
        
        return $this->save();
    }

    public function reject(string $reason): bool
    {
        $this->status = 'rejected';
        $this->review_date = now();
        $this->reviewed_by = auth()->id();
        $this->rejection_reason = $reason;
        
        return $this->save();
    }

    public function markAsPaid(string $paymentMethod = null, string $reference = null): bool
    {
        $this->status = 'paid';
        $this->payment_date = now();
        $this->payment_method = $paymentMethod;
        $this->payment_reference = $reference;
        
        return $this->save();
    }

    // Scopes
    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeUnderReview($query)
    {
        return $query->where('status', 'under_review');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeAutoGenerated($query)
    {
        return $query->where('auto_generated', true);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByDriver($query, int $driverId)
    {
        return $query->where('driver_id', $driverId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    public function scopePendingApproval($query)
    {
        return $query->whereIn('status', ['submitted', 'under_review']);
    }

    public function scopeAwaitingPayment($query)
    {
        return $query->where('status', 'approved');
    }
}
