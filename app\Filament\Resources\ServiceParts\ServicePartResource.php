<?php

namespace App\Filament\Resources\ServiceParts;

use App\Filament\Resources\ServiceParts\Pages\ManageServiceParts;
use App\Models\ServicePart;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use UnitEnum;

class ServicePartResource extends Resource
{
    protected static ?string $model = ServicePart::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::Cog8Tooth;
    protected static string|UnitEnum|null $navigationGroup = 'Service Management';
    protected static ?int $navigationSort = 4;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Part Information')
                    ->schema([
                        Select::make('service_id')
                            ->relationship('service', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Service'),
                        TextInput::make('part_number')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('part_name')
                            ->required()
                            ->maxLength(255),
                        Textarea::make('description')
                            ->rows(3),
                        TextInput::make('manufacturer')
                            ->maxLength(255),
                        TextInput::make('supplier')
                            ->maxLength(255),
                    ])
                    ->columnSpanFull()
                    ->columns(2),

                Section::make('Quantity & Cost')
                    ->schema([
                        TextInput::make('quantity_used')
                            ->numeric()
                            ->required()
                            ->minValue(1),
                        TextInput::make('unit_cost')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->required(),
                        TextInput::make('total_cost')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->required(),
                        Select::make('part_condition')
                            ->options([
                                'new' => 'New',
                                'refurbished' => 'Refurbished',
                                'used' => 'Used',
                            ])
                            ->default('new')
                            ->required(),
                    ])
                    ->columnSpanFull()
                    ->columns(2),

                Section::make('Warranty Information')
                    ->schema([
                        Toggle::make('is_warranty_covered')
                            ->label('Warranty Covered'),
                        DatePicker::make('warranty_start_date')
                            ->label('Warranty Start Date'),
                        DatePicker::make('warranty_expiry_date')
                            ->label('Warranty Expiry Date'),
                        TextInput::make('warranty_duration_months')
                            ->numeric()
                            ->label('Warranty Duration (Months)'),
                        Textarea::make('warranty_terms')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columnSpanFull()
                    ->columns(2),

                Section::make('Additional Information')
                    ->schema([
                        TextInput::make('serial_number')
                            ->maxLength(255),
                        TextInput::make('batch_number')
                            ->maxLength(255),
                        Textarea::make('installation_notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columnSpanFull()
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('service.name')
                    ->searchable()
                    ->sortable()
                    ->label('Service'),
                TextColumn::make('part_number')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('part_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('manufacturer')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('supplier')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('quantity_used')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('unit_cost')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('total_cost')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('part_condition')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'new' => 'success',
                        'refurbished' => 'warning',
                        'used' => 'gray',
                    }),
                IconColumn::make('is_warranty_covered')
                    ->boolean()
                    ->label('Warranty'),
                TextColumn::make('warranty_expiry_date')
                    ->date()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('part_condition')
                    ->options([
                        'new' => 'New',
                        'refurbished' => 'Refurbished',
                        'used' => 'Used',
                    ]),
                SelectFilter::make('is_warranty_covered')
                    ->options([
                        1 => 'Warranty Covered',
                        0 => 'No Warranty',
                    ])
                    ->label('Warranty Status'),
            ])
            ->recordActions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => ManageServiceParts::route('/'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
