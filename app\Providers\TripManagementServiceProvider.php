<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Trip;
use App\Observers\TripObserver;
use App\Services\TripAutomationService;
use App\Services\TripDataImportService;

class TripManagementServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register services
        $this->app->singleton(TripAutomationService::class);
        $this->app->singleton(TripDataImportService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register model observers
        Trip::observe(TripObserver::class);
    }
}
