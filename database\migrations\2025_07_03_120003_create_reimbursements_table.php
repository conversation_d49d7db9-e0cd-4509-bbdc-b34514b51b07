<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reimbursements', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('trip_id')->constrained()->cascadeOnDelete();
            $table->foreignId('driver_id')->constrained()->cascadeOnDelete();
            
            // Reimbursement Details
            $table->string('reimbursement_no')->unique();
            $table->enum('category', [
                'fuel', 'tolls', 'parking', 'meals', 'accommodation', 
                'repairs', 'fines', 'communication', 'medical', 'miscellaneous'
            ]);
            
            // Financial Data
            $table->decimal('amount_claimed', 10, 2);
            $table->decimal('amount_approved', 10, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->decimal('exchange_rate', 8, 4)->default(1.0000);
            $table->decimal('amount_local_currency', 10, 2)->nullable(); // Auto-calculated
            
            // Expense Details
            $table->date('expense_date');
            $table->string('vendor_name')->nullable();
            $table->string('receipt_number')->nullable();
            $table->text('description');
            $table->string('location')->nullable();
            
            // Approval Workflow
            $table->enum('status', ['submitted', 'under_review', 'approved', 'rejected', 'paid'])
                  ->default('submitted');
            $table->date('submission_date');
            $table->date('review_date')->nullable();
            $table->date('approval_date')->nullable();
            $table->date('payment_date')->nullable();
            
            // Payment Information
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->text('reviewer_notes')->nullable();
            
            // Documentation
            $table->json('receipt_documents')->nullable(); // File paths
            $table->json('supporting_documents')->nullable(); // Additional files
            
            // Auto-generation flags
            $table->boolean('auto_generated')->default(false);
            $table->json('auto_generation_rules')->nullable(); // Rules used for auto-generation
            
            // Audit Fields
            $table->foreignId('submitted_by')->constrained('users');
            $table->foreignId('reviewed_by')->nullable()->constrained('users');
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Indexes
            $table->index(['trip_id', 'category']);
            $table->index(['driver_id', 'expense_date']);
            $table->index(['status', 'submission_date']);
            $table->index('reimbursement_no');
            $table->index(['expense_date', 'amount_claimed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reimbursements');
    }
};
