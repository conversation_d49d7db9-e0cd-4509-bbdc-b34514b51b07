<?php

namespace App\Filament\Resources\Trips\Pages;

use App\Filament\Resources\Trips\TripResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTrip extends EditRecord
{
    protected static string $resource = TripResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            
            Actions\Action::make('generate_automation')
                ->label('Run Automation')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('primary')
                ->action(function () {
                    $trip = $this->record;
                    $generatedCount = 0;
                    
                    // Generate sobbies if enabled and none exist
                    if ($trip->auto_generate_sobbies && $trip->sobbies()->count() === 0) {
                        $sobbies = \App\Models\Sobby::generateForTrip($trip);
                        foreach ($sobbies as $sobby) {
                            $sobby->save();
                            $generatedCount++;
                        }
                    }
                    
                    // Generate reimbursements if enabled and none exist
                    if ($trip->auto_generate_reimbursements && $trip->reimbursements()->count() === 0) {
                        $reimbursements = \App\Models\Reimbursement::generateForTrip($trip);
                        foreach ($reimbursements as $reimbursement) {
                            $reimbursement->save();
                            $generatedCount++;
                        }
                    }
                    
                    // Check for shortages if enabled
                    if ($trip->auto_calculate_shortages && $trip->quantity_loaded && $trip->quantity_offloaded) {
                        $shortages = \App\Models\Shortage::generateForTrip($trip);
                        foreach ($shortages as $shortage) {
                            $shortage->save();
                            $generatedCount++;
                        }
                    }
                    
                    \Filament\Notifications\Notification::make()
                        ->title('Automation Complete')
                        ->body("Generated {$generatedCount} automated records.")
                        ->success()
                        ->send();
                }),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['updated_by'] = auth()->id();
        
        return $data;
    }
}
