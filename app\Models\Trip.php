<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Trip extends Model
{
    protected $guarded = [];

    protected $casts = [
        'trip_start_date' => 'date',
        'date_arrived_at_loading' => 'date',
        'load_date' => 'date',
        'date_arrived_at_offloading' => 'date',
        'date_offloaded' => 'date',
        'trip_end_date' => 'date',
        'invoice_date' => 'date',
        'invoice_submission_date' => 'date',
        'due_date' => 'date',
        'date_received' => 'date',

        // Decimal fields
        'loading_quantity' => 'decimal:2',
        'quantity_loaded' => 'decimal:2',
        'quantity_offloaded' => 'decimal:2',
        'quantity_diff' => 'decimal:2',
        'loading_first_weight' => 'decimal:2',
        'loading_second_weight' => 'decimal:2',
        'loading_net_weight' => 'decimal:2',
        'offloading_first_weight' => 'decimal:2',
        'offloading_second_weight' => 'decimal:2',
        'offloading_net_weight' => 'decimal:2',
        'rate_usd_per_mt' => 'decimal:2',
        'usd_price_per_ton' => 'decimal:2',
        'usd_invoice_value' => 'decimal:2',
        'cd3_amount' => 'decimal:2',
        'shortage_deduction' => 'decimal:2',
        'upfront_payment' => 'decimal:2',
        'invoice_total' => 'decimal:2',
        'balance_due' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'invoicing_rate' => 'decimal:2',

        // Boolean fields
        'auto_generate_sobbies' => 'boolean',
        'auto_generate_reimbursements' => 'boolean',
        'auto_calculate_shortages' => 'boolean',
        'is_automated_calculations_enabled' => 'boolean',
    ];

    // Relationships
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function sobbies(): HasMany
    {
        return $this->hasMany(Sobby::class);
    }

    public function reimbursements(): HasMany
    {
        return $this->hasMany(Reimbursement::class);
    }

    public function shortages(): HasMany
    {
        return $this->hasMany(Shortage::class);
    }

    // Automated Calculations - Accessors
    protected function quantityDiff(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateQuantityDifference(),
        );
    }

    protected function loadingNetWeight(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateLoadingNetWeight(),
        );
    }

    protected function offloadingNetWeight(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateOffloadingNetWeight(),
        );
    }

    protected function usdInvoiceValue(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateInvoiceValue(),
        );
    }

    protected function invoiceTotal(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateInvoiceTotal(),
        );
    }

    protected function balanceDue(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateBalanceDue(),
        );
    }

    protected function dueDate(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->calculateDueDate(),
        );
    }

    // Calculation Methods
    private function calculateQuantityDifference(): ?float
    {
        if ($this->quantity_loaded && $this->quantity_offloaded) {
            return round($this->quantity_loaded - $this->quantity_offloaded, 2);
        }
        return null;
    }

    private function calculateLoadingNetWeight(): ?float
    {
        if ($this->loading_first_weight && $this->loading_second_weight) {
            return round(abs($this->loading_first_weight - $this->loading_second_weight), 2);
        }
        return null;
    }

    private function calculateOffloadingNetWeight(): ?float
    {
        if ($this->offloading_first_weight && $this->offloading_second_weight) {
            return round(abs($this->offloading_first_weight - $this->offloading_second_weight), 2);
        }
        return null;
    }

    private function calculateInvoiceValue(): ?float
    {
        $rate = $this->rate_usd_per_mt ?? $this->usd_price_per_ton;
        $quantity = $this->quantity_loaded ?? $this->loading_quantity;

        if ($rate && $quantity) {
            return round($rate * $quantity, 2);
        }
        return null;
    }

    private function calculateInvoiceTotal(): ?float
    {
        $invoiceValue = $this->usd_invoice_value ?? $this->calculateInvoiceValue();

        if ($invoiceValue) {
            return round($invoiceValue - $this->shortage_deduction, 2);
        }
        return null;
    }

    private function calculateBalanceDue(): ?float
    {
        $total = $this->invoice_total ?? $this->calculateInvoiceTotal();

        if ($total) {
            return round($total - $this->upfront_payment - $this->amount_paid, 2);
        }
        return null;
    }

    private function calculateDueDate(): ?Carbon
    {
        if ($this->invoice_date && $this->credit_days) {
            return $this->invoice_date->addDays($this->credit_days);
        }
        return null;
    }

    // Business Logic Methods
    public function getTripDurationDays(): ?int
    {
        if ($this->trip_start_date && $this->trip_end_date) {
            return $this->trip_start_date->diffInDays($this->trip_end_date) + 1;
        }
        return null;
    }

    public function getShortagePercentage(): ?float
    {
        $diff = $this->quantity_diff;
        $loaded = $this->quantity_loaded ?? $this->loading_quantity;

        if ($diff && $loaded && $loaded > 0) {
            return round(($diff / $loaded) * 100, 2);
        }
        return null;
    }

    public function isOverdue(): bool
    {
        $dueDate = $this->due_date ?? $this->calculateDueDate();
        return $dueDate && $dueDate->isPast() && $this->balance_due > 0;
    }

    public function hasSignificantShortage(): bool
    {
        $shortagePercentage = $this->getShortagePercentage();
        $threshold = TripAutomationSetting::getValue('shortage_threshold_percentage', 2.0);

        return $shortagePercentage && $shortagePercentage > $threshold;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['cancelled']);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->whereNotNull('due_date')
            ->where('due_date', '<', now())
            ->where('balance_due', '>', 0);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('trip_start_date', [$startDate, $endDate]);
    }

    public function scopeByClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    public function scopeByDriver($query, $driverId)
    {
        return $query->where('driver_id', $driverId);
    }

    public function scopeByVehicle($query, $vehicleId)
    {
        return $query->where('vehicle_id', $vehicleId);
    }
}
