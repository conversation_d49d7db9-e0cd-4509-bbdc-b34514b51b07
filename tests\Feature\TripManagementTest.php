<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Trip;
use App\Models\Vehicle;
use App\Models\Driver;
use App\Models\Client;
use App\Models\User;
use App\Models\VehicleType;
use App\Services\TripAutomationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class TripManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Vehicle $vehicle;
    protected Driver $driver;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Create required related models
        $vehicleType = VehicleType::create([
            'type' => 'Truck',
            'description' => 'Test Truck Type',
        ]);

        $this->vehicle = Vehicle::create([
            'vehicle_type_id' => $vehicleType->id,
            'name' => 'TEST-TRUCK-001',
            'model' => 'Test Model',
            'plate_number' => 'TEST-123',
            'engine_type' => 'Diesel',
            'engine_number' => 'ENG-TEST-001',
            'registration_date' => '2023-01-01',
            'color' => 'White',
            'document' => 'test_doc.pdf',
        ]);

        $this->driver = Driver::create([
            'name' => 'Test Driver',
            'email' => '<EMAIL>',
            'phone' => '+234-************',
            'gender' => 'male',
            'date_of_birth' => '1985-01-01',
            'joining_date' => '2023-01-01',
            'address' => 'Test Address',
            'license_number' => 'TEST-LICENSE-001',
            'license_issue_date' => '2020-01-01',
            'license_expiry_date' => '2025-01-01',
        ]);

        $this->client = Client::create([
            'name' => 'Test Client Ltd',
            'email' => '<EMAIL>',
            'phone' => '+234-************',
            'address' => 'Test Client Address',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'Nigeria',
        ]);
    }

    public function test_can_create_trip_with_basic_data(): void
    {
        $tripData = [
            'trip_no' => 'TEST-TRIP-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'rate_usd_per_mt' => 850.00,
            'created_by' => $this->user->id,
        ];

        $trip = Trip::create($tripData);

        $this->assertDatabaseHas('trips', [
            'trip_no' => 'TEST-TRIP-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
        ]);

        $this->assertEquals('TEST-TRIP-001', $trip->trip_no);
        $this->assertEquals($this->vehicle->id, $trip->vehicle_id);
        $this->assertEquals($this->driver->id, $trip->driver_id);
        $this->assertEquals($this->client->id, $trip->client_id);
    }

    public function test_trip_calculates_financial_fields_automatically(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-002',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'rate_usd_per_mt' => 850.00,
            'quantity_loaded' => 30000.00,
            'quantity_offloaded' => 29850.00,
            'upfront_payment' => 5000.00,
            'amount_paid' => 2000.00,
            'created_by' => $this->user->id,
        ]);

        // Test automatic calculations
        $this->assertEquals(150.00, $trip->quantity_diff); // 30000 - 29850
        $this->assertEquals(25500000.00, $trip->usd_invoice_value); // 30000 * 850
        $this->assertEquals(25500000.00, $trip->invoice_total); // invoice_value - shortage_deduction (0)
        $this->assertEquals(25493000.00, $trip->balance_due); // invoice_total - upfront - paid
    }

    public function test_trip_calculates_weight_differences(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-003',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'loading_first_weight' => 45000.00,
            'loading_second_weight' => 15000.00,
            'offloading_first_weight' => 44850.00,
            'offloading_second_weight' => 15000.00,
            'created_by' => $this->user->id,
        ]);

        $this->assertEquals(30000.00, $trip->loading_net_weight); // |45000 - 15000|
        $this->assertEquals(29850.00, $trip->offloading_net_weight); // |44850 - 15000|
    }

    public function test_trip_automation_service_generates_sobbies(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-004',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'trip_end_date' => Carbon::now()->addDays(3),
            'auto_generate_sobbies' => true,
            'created_by' => $this->user->id,
        ]);

        $automationService = app(TripAutomationService::class);
        $results = $automationService->processTrip($trip);

        $this->assertArrayHasKey('sobbies', $results);
        $this->assertGreaterThan(0, count($results['sobbies']));
        
        // Check that sobbies were actually created in database
        $this->assertGreaterThan(0, $trip->sobbies()->count());
    }

    public function test_trip_automation_service_generates_reimbursements(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-005',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'trip_end_date' => Carbon::now()->addDays(3),
            'auto_generate_reimbursements' => true,
            'created_by' => $this->user->id,
        ]);

        $automationService = app(TripAutomationService::class);
        $results = $automationService->processTrip($trip);

        $this->assertArrayHasKey('reimbursements', $results);
        
        // Check that reimbursements were created in database
        $this->assertGreaterThanOrEqual(0, $trip->reimbursements()->count());
    }

    public function test_trip_automation_service_detects_shortages(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-006',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'quantity_loaded' => 30000.00,
            'quantity_offloaded' => 29400.00, // 600 shortage (2% shortage)
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'rate_usd_per_mt' => 850.00,
            'auto_calculate_shortages' => true,
            'created_by' => $this->user->id,
        ]);

        $automationService = app(TripAutomationService::class);
        $results = $automationService->processTrip($trip);

        $this->assertArrayHasKey('shortages', $results);
        
        // Should detect shortage since 2% is above default threshold
        if (count($results['shortages']) > 0) {
            $this->assertGreaterThan(0, $trip->shortages()->count());
        }
    }

    public function test_trip_due_date_calculation(): void
    {
        $invoiceDate = Carbon::now();
        $creditDays = 30;

        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-007',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'invoice_date' => $invoiceDate,
            'credit_days' => $creditDays,
            'created_by' => $this->user->id,
        ]);

        $expectedDueDate = $invoiceDate->copy()->addDays($creditDays);
        $this->assertEquals($expectedDueDate->toDateString(), $trip->due_date->toDateString());
    }

    public function test_trip_overdue_detection(): void
    {
        $pastDate = Carbon::now()->subDays(45);

        $trip = Trip::create([
            'trip_no' => 'TEST-TRIP-008',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'invoice_date' => $pastDate,
            'credit_days' => 30,
            'rate_usd_per_mt' => 850.00,
            'balance_due' => 1000.00,
            'created_by' => $this->user->id,
        ]);

        $this->assertTrue($trip->isOverdue());
    }

    public function test_trip_scopes_work_correctly(): void
    {
        // Create trips with different statuses
        $activeTrip = Trip::create([
            'trip_no' => 'ACTIVE-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'status' => 'in_progress',
            'created_by' => $this->user->id,
        ]);

        $cancelledTrip = Trip::create([
            'trip_no' => 'CANCELLED-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'status' => 'cancelled',
            'created_by' => $this->user->id,
        ]);

        // Test active scope
        $activeTrips = Trip::active()->get();
        $this->assertTrue($activeTrips->contains($activeTrip));
        $this->assertFalse($activeTrips->contains($cancelledTrip));

        // Test by client scope
        $clientTrips = Trip::byClient($this->client->id)->get();
        $this->assertTrue($clientTrips->contains($activeTrip));
        $this->assertTrue($clientTrips->contains($cancelledTrip));
    }
}
