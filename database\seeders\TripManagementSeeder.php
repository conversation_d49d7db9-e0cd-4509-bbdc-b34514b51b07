<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Trip;
use App\Models\Vehicle;
use App\Models\Driver;
use App\Models\Client;
use App\Models\User;
use App\Services\TripAutomationService;
use Carbon\Carbon;

class TripManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have required related data
        $this->ensureRequiredData();

        // Create sample trips
        $this->createSampleTrips();
    }

    /**
     * Ensure required related data exists
     */
    private function ensureRequiredData(): void
    {
        // Create a default user if none exists
        if (User::count() === 0) {
            User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        // Create sample vehicles if none exist
        if (Vehicle::count() === 0) {
            $vehicleType = \App\Models\VehicleType::first();
            if (!$vehicleType) {
                $vehicleType = \App\Models\VehicleType::create([
                    'type' => 'Truck',
                    'description' => 'Heavy Duty Truck',
                ]);
            }

            Vehicle::create([
                'vehicle_type_id' => $vehicleType->id,
                'name' => 'TRUCK-001',
                'model' => 'Mercedes Actros',
                'plate_number' => 'ABC-123-XY',
                'engine_type' => 'Diesel',
                'engine_number' => 'ENG001',
                'registration_date' => '2023-01-15',
                'color' => 'White',
                'document' => 'truck_001_docs.pdf',
            ]);

            Vehicle::create([
                'vehicle_type_id' => $vehicleType->id,
                'name' => 'TRUCK-002',
                'model' => 'Volvo FH',
                'plate_number' => 'DEF-456-ZW',
                'engine_type' => 'Diesel',
                'engine_number' => 'ENG002',
                'registration_date' => '2023-03-20',
                'color' => 'Blue',
                'document' => 'truck_002_docs.pdf',
            ]);
        }

        // Create sample drivers if none exist
        if (Driver::count() === 0) {
            Driver::create([
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'gender' => 'male',
                'date_of_birth' => '1985-05-15',
                'joining_date' => '2023-01-01',
                'address' => '123 Main Street, Lagos, Nigeria',
                'license_number' => 'DL001234567',
                'license_issue_date' => '2020-01-01',
                'license_expiry_date' => '2025-01-01',
                'license_document' => 'john_doe_license.pdf',
            ]);

            Driver::create([
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'gender' => 'female',
                'date_of_birth' => '1988-08-22',
                'joining_date' => '2023-02-15',
                'address' => '456 Oak Avenue, Abuja, Nigeria',
                'license_number' => 'DL002345678',
                'license_issue_date' => '2021-03-15',
                'license_expiry_date' => '2026-03-15',
                'license_document' => 'jane_smith_license.pdf',
            ]);
        }

        // Create sample clients if none exist
        if (Client::count() === 0) {
            Client::create([
                'name' => 'ABC Oil Company Ltd',
                'email' => '<EMAIL>',
                'phone' => '+***********-5678',
                'address' => '789 Business District',
                'city' => 'Lagos',
                'state' => 'Lagos State',
                'postal_code' => '100001',
                'country' => 'Nigeria',
                'notes' => 'Major oil distribution company',
            ]);

            Client::create([
                'name' => 'XYZ Petroleum Ltd',
                'email' => '<EMAIL>',
                'phone' => '+***********-6789',
                'address' => '321 Industrial Area',
                'city' => 'Port Harcourt',
                'state' => 'Rivers State',
                'postal_code' => '500001',
                'country' => 'Nigeria',
                'notes' => 'Regional petroleum distributor',
            ]);
        }
    }

    /**
     * Create sample trips
     */
    private function createSampleTrips(): void
    {
        $vehicles = Vehicle::all();
        $drivers = Driver::all();
        $clients = Client::all();
        $user = User::first();

        $sampleTrips = [
            [
                'trip_no' => 'TRP-2025-001',
                'cd3_no' => 'CD3-2025-001',
                'manifest_no' => 'MAN-2025-001',
                'vehicle_id' => $vehicles->first()->id,
                'driver_id' => $drivers->first()->id,
                'client_id' => $clients->first()->id,
                'truck_reg' => $vehicles->first()->plate_number,
                'trailer_reg' => 'TRL-001-XY',
                'passport' => $drivers->first()->license_number,
                'product' => 'Diesel',
                'cargo_type' => 'liquid',
                'loading_quantity' => 30000.00,
                'route' => 'Lagos to Abuja',
                'loading_point' => 'Apapa Terminal',
                'trip_start_date' => Carbon::now()->subDays(10),
                'date_arrived_at_loading' => Carbon::now()->subDays(10),
                'load_date' => Carbon::now()->subDays(9),
                'date_arrived_at_offloading' => Carbon::now()->subDays(7),
                'date_offloaded' => Carbon::now()->subDays(7),
                'trip_end_date' => Carbon::now()->subDays(6),
                'quantity_loaded' => 30000.00,
                'quantity_offloaded' => 29850.00, // 150 shortage
                'loading_first_weight' => 45000.00,
                'loading_second_weight' => 15000.00,
                'offloading_first_weight' => 44850.00,
                'offloading_second_weight' => 15000.00,
                'rate_usd_per_mt' => 850.00,
                'cd3_amount' => 25500000.00,
                'upfront_payment' => 5000000.00,
                'invoice_no' => 'INV-2025-001',
                'invoice_date' => Carbon::now()->subDays(5),
                'credit_days' => 30,
                'status' => 'completed',
                'created_by' => $user->id,
            ],
            [
                'trip_no' => 'TRP-2025-002',
                'cd3_no' => 'CD3-2025-002',
                'manifest_no' => 'MAN-2025-002',
                'vehicle_id' => $vehicles->count() > 1 ? $vehicles->skip(1)->first()->id : $vehicles->first()->id,
                'driver_id' => $drivers->count() > 1 ? $drivers->skip(1)->first()->id : $drivers->first()->id,
                'client_id' => $clients->count() > 1 ? $clients->skip(1)->first()->id : $clients->first()->id,
                'truck_reg' => $vehicles->count() > 1 ? $vehicles->skip(1)->first()->plate_number : $vehicles->first()->plate_number,
                'trailer_reg' => 'TRL-002-ZW',
                'passport' => $drivers->count() > 1 ? $drivers->skip(1)->first()->license_number : $drivers->first()->license_number,
                'product' => 'Petrol',
                'cargo_type' => 'liquid',
                'loading_quantity' => 25000.00,
                'route' => 'Port Harcourt to Kano',
                'loading_point' => 'Port Harcourt Refinery',
                'trip_start_date' => Carbon::now()->subDays(5),
                'date_arrived_at_loading' => Carbon::now()->subDays(5),
                'load_date' => Carbon::now()->subDays(4),
                'quantity_loaded' => 25000.00,
                'loading_first_weight' => 40000.00,
                'loading_second_weight' => 15000.00,
                'rate_usd_per_mt' => 920.00,
                'cd3_amount' => 23000000.00,
                'status' => 'in_progress',
                'created_by' => $user->id,
            ],
            [
                'trip_no' => 'TRP-2025-003',
                'vehicle_id' => $vehicles->first()->id,
                'driver_id' => $drivers->first()->id,
                'client_id' => $clients->first()->id,
                'truck_reg' => $vehicles->first()->plate_number,
                'product' => 'Crude Oil',
                'cargo_type' => 'liquid',
                'loading_quantity' => 35000.00,
                'route' => 'Warri to Lagos',
                'loading_point' => 'Warri Refinery',
                'trip_start_date' => Carbon::now()->addDays(2),
                'rate_usd_per_mt' => 780.00,
                'status' => 'draft',
                'created_by' => $user->id,
            ],
        ];

        foreach ($sampleTrips as $tripData) {
            $trip = Trip::create($tripData);
            
            // Run automation for completed trips
            if ($trip->status === 'completed') {
                $automationService = app(TripAutomationService::class);
                $automationService->processTrip($trip);
            }
        }
    }
}
