<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sobbies', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('trip_id')->constrained()->cascadeOnDelete();
            $table->foreignId('driver_id')->constrained()->cascadeOnDelete();
            
            // Sobby Details
            $table->string('sobby_no')->unique();
            $table->enum('type', ['fuel_allowance', 'meal_allowance', 'accommodation', 'miscellaneous'])
                  ->default('fuel_allowance');
            
            // Financial Data
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->decimal('exchange_rate', 8, 4)->default(1.0000);
            $table->decimal('amount_local_currency', 10, 2)->nullable(); // Auto-calculated
            
            // Trip-based Calculations
            $table->decimal('trip_distance_km', 8, 2)->nullable();
            $table->decimal('rate_per_km', 6, 4)->nullable();
            $table->integer('trip_duration_days')->nullable();
            $table->decimal('daily_allowance_rate', 8, 2)->nullable();
            
            // Status and Dates
            $table->enum('status', ['pending', 'approved', 'paid', 'cancelled'])->default('pending');
            $table->date('issue_date');
            $table->date('payment_date')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            
            // Documentation
            $table->text('description')->nullable();
            $table->text('notes')->nullable();
            $table->json('supporting_documents')->nullable(); // File paths
            
            // Auto-generation flags
            $table->boolean('auto_generated')->default(false);
            $table->json('calculation_details')->nullable(); // Store calculation breakdown
            
            // Audit Fields
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Indexes
            $table->index(['trip_id', 'type']);
            $table->index(['driver_id', 'issue_date']);
            $table->index(['status', 'issue_date']);
            $table->index('sobby_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sobbies');
    }
};
