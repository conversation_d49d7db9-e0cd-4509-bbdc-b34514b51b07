<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Trip;
use App\Models\Vehicle;
use App\Models\Driver;
use App\Models\Client;
use App\Models\User;
use App\Models\VehicleType;
use App\Models\TripAutomationSetting;
use App\Services\TripAutomationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class TripAutomationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TripAutomationService $automationService;
    protected User $user;
    protected Vehicle $vehicle;
    protected Driver $driver;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->automationService = new TripAutomationService();
        
        // Create test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Seed automation settings
        TripAutomationSetting::seedDefaultSettings();

        // Create required related models
        $vehicleType = VehicleType::create([
            'type' => 'Truck',
            'description' => 'Test Truck Type',
        ]);

        $this->vehicle = Vehicle::create([
            'vehicle_type_id' => $vehicleType->id,
            'name' => 'TEST-TRUCK-001',
            'model' => 'Test Model',
            'plate_number' => 'TEST-123',
            'engine_type' => 'Diesel',
            'engine_number' => 'ENG-TEST-001',
            'registration_date' => '2023-01-01',
            'color' => 'White',
            'document' => 'test_doc.pdf',
        ]);

        $this->driver = Driver::create([
            'name' => 'Test Driver',
            'email' => '<EMAIL>',
            'phone' => '+234-************',
            'gender' => 'male',
            'date_of_birth' => '1985-01-01',
            'joining_date' => '2023-01-01',
            'address' => 'Test Address',
            'license_number' => 'TEST-LICENSE-001',
            'license_issue_date' => '2020-01-01',
            'license_expiry_date' => '2025-01-01',
        ]);

        $this->client = Client::create([
            'name' => 'Test Client Ltd',
            'email' => '<EMAIL>',
            'phone' => '+234-************',
            'address' => 'Test Client Address',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'Nigeria',
        ]);
    }

    public function test_validates_trip_for_automation(): void
    {
        $trip = new Trip([
            'trip_no' => 'TEST-001',
            'auto_generate_sobbies' => true,
            'auto_generate_reimbursements' => true,
            'auto_calculate_shortages' => true,
        ]);

        $errors = $this->automationService->validateTripForAutomation($trip);

        $this->assertNotEmpty($errors);
        $this->assertContains('Trip start date is required for sobby generation', $errors);
        $this->assertContains('Driver is required for sobby generation', $errors);
    }

    public function test_validates_trip_passes_with_complete_data(): void
    {
        $trip = new Trip([
            'trip_no' => 'TEST-001',
            'trip_start_date' => Carbon::now(),
            'driver_id' => $this->driver->id,
            'quantity_loaded' => 30000.00,
            'quantity_offloaded' => 29850.00,
            'auto_generate_sobbies' => true,
            'auto_generate_reimbursements' => true,
            'auto_calculate_shortages' => true,
        ]);

        $errors = $this->automationService->validateTripForAutomation($trip);

        $this->assertEmpty($errors);
    }

    public function test_processes_trip_with_all_automation_enabled(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-AUTOMATION-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'quantity_loaded' => 30000.00,
            'quantity_offloaded' => 29400.00, // 2% shortage
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'trip_end_date' => Carbon::now()->addDays(3),
            'rate_usd_per_mt' => 850.00,
            'auto_generate_sobbies' => true,
            'auto_generate_reimbursements' => true,
            'auto_calculate_shortages' => true,
            'created_by' => $this->user->id,
        ]);

        $results = $this->automationService->processTrip($trip);

        $this->assertArrayHasKey('sobbies', $results);
        $this->assertArrayHasKey('reimbursements', $results);
        $this->assertArrayHasKey('shortages', $results);
        $this->assertArrayHasKey('errors', $results);

        $this->assertEmpty($results['errors']);
    }

    public function test_gets_automation_summary(): void
    {
        $trip = Trip::create([
            'trip_no' => 'TEST-SUMMARY-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'rate_usd_per_mt' => 850.00,
            'auto_generate_sobbies' => true,
            'auto_generate_reimbursements' => false,
            'auto_calculate_shortages' => true,
            'created_by' => $this->user->id,
        ]);

        $summary = $this->automationService->getAutomationSummary($trip);

        $this->assertArrayHasKey('trip_id', $summary);
        $this->assertArrayHasKey('trip_no', $summary);
        $this->assertArrayHasKey('automation_enabled', $summary);
        $this->assertArrayHasKey('generated_records', $summary);
        $this->assertArrayHasKey('financial_summary', $summary);
        $this->assertArrayHasKey('validation_errors', $summary);

        $this->assertEquals($trip->id, $summary['trip_id']);
        $this->assertEquals('TEST-SUMMARY-001', $summary['trip_no']);
        $this->assertTrue($summary['automation_enabled']['sobbies']);
        $this->assertFalse($summary['automation_enabled']['reimbursements']);
        $this->assertTrue($summary['automation_enabled']['shortages']);
    }

    public function test_bulk_processes_multiple_trips(): void
    {
        $trip1 = Trip::create([
            'trip_no' => 'BULK-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'auto_generate_sobbies' => true,
            'created_by' => $this->user->id,
        ]);

        $trip2 = Trip::create([
            'trip_no' => 'BULK-002',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 25000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'auto_generate_sobbies' => true,
            'created_by' => $this->user->id,
        ]);

        $results = $this->automationService->bulkProcessTrips([$trip1->id, $trip2->id]);

        $this->assertArrayHasKey('processed', $results);
        $this->assertArrayHasKey('failed', $results);
        $this->assertArrayHasKey('errors', $results);

        $this->assertEquals(2, $results['processed']);
        $this->assertEquals(0, $results['failed']);
    }

    public function test_recalculates_financials(): void
    {
        $trip = Trip::create([
            'trip_no' => 'RECALC-001',
            'vehicle_id' => $this->vehicle->id,
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'quantity_loaded' => 30000.00,
            'quantity_offloaded' => 29850.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'rate_usd_per_mt' => 850.00,
            'created_by' => $this->user->id,
        ]);

        // Create a shortage manually
        $trip->shortages()->create([
            'client_id' => $this->client->id,
            'shortage_no' => 'SHORT-001',
            'type' => 'quantity_shortage',
            'expected_quantity' => 30000.00,
            'actual_quantity' => 29850.00,
            'unit_rate' => 850.00,
            'identified_date' => Carbon::now(),
            'status' => 'identified',
            'description' => 'Test shortage',
            'identified_by' => $this->user->id,
        ]);

        $this->automationService->recalculateFinancials($trip);

        $trip->refresh();
        $this->assertGreaterThan(0, $trip->shortage_deduction);
    }

    public function test_handles_automation_errors_gracefully(): void
    {
        // Create trip with invalid data that should cause errors
        $trip = Trip::create([
            'trip_no' => 'ERROR-001',
            'vehicle_id' => 999999, // Non-existent vehicle
            'driver_id' => $this->driver->id,
            'client_id' => $this->client->id,
            'product' => 'Test Product',
            'cargo_type' => 'liquid',
            'loading_quantity' => 30000.00,
            'route' => 'Test Route',
            'loading_point' => 'Test Loading Point',
            'trip_start_date' => Carbon::now(),
            'auto_generate_sobbies' => true,
            'created_by' => $this->user->id,
        ]);

        $results = $this->automationService->processTrip($trip);

        // Should handle errors gracefully and not crash
        $this->assertArrayHasKey('errors', $results);
        $this->assertIsArray($results['errors']);
    }
}
