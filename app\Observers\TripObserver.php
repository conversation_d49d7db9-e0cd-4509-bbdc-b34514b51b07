<?php

namespace App\Observers;

use App\Models\Trip;
use App\Services\TripAutomationService;

class TripObserver
{
    private TripAutomationService $automationService;

    public function __construct(TripAutomationService $automationService)
    {
        $this->automationService = $automationService;
    }

    /**
     * Handle the Trip "created" event.
     */
    public function created(Trip $trip): void
    {
        // Set created_by if not already set
        if (!$trip->created_by) {
            $trip->created_by = auth()->id() ?? 1;
            $trip->saveQuietly(); // Save without triggering events
        }
    }

    /**
     * Handle the Trip "updated" event.
     */
    public function updated(Trip $trip): void
    {
        // Set updated_by
        if (auth()->check()) {
            $trip->updated_by = auth()->id();
            $trip->saveQuietly();
        }

        // Check if quantity fields changed and recalculate shortages
        if ($trip->auto_calculate_shortages && $this->quantityFieldsChanged($trip)) {
            $this->automationService->recalculateFinancials($trip);
        }
    }

    /**
     * Handle the Trip "saving" event.
     */
    public function saving(Trip $trip): void
    {
        // Auto-calculate fields if automation is enabled
        if ($trip->is_automated_calculations_enabled) {
            $this->calculateAutomaticFields($trip);
        }
    }

    /**
     * Calculate automatic fields
     */
    private function calculateAutomaticFields(Trip $trip): void
    {
        // Calculate quantity difference
        if ($trip->quantity_loaded && $trip->quantity_offloaded) {
            $trip->quantity_diff = round($trip->quantity_loaded - $trip->quantity_offloaded, 2);
        }

        // Calculate loading net weight
        if ($trip->loading_first_weight && $trip->loading_second_weight) {
            $trip->loading_net_weight = round(abs($trip->loading_first_weight - $trip->loading_second_weight), 2);
        }

        // Calculate offloading net weight
        if ($trip->offloading_first_weight && $trip->offloading_second_weight) {
            $trip->offloading_net_weight = round(abs($trip->offloading_first_weight - $trip->offloading_second_weight), 2);
        }

        // Calculate invoice value
        $rate = $trip->rate_usd_per_mt ?? $trip->usd_price_per_ton;
        $quantity = $trip->quantity_loaded ?? $trip->loading_quantity;
        
        if ($rate && $quantity) {
            $trip->usd_invoice_value = round($rate * $quantity, 2);
        }

        // Calculate invoice total
        if ($trip->usd_invoice_value) {
            $trip->invoice_total = round($trip->usd_invoice_value - ($trip->shortage_deduction ?? 0), 2);
        }

        // Calculate balance due
        if ($trip->invoice_total) {
            $trip->balance_due = round(
                $trip->invoice_total - ($trip->upfront_payment ?? 0) - ($trip->amount_paid ?? 0), 
                2
            );
        }

        // Calculate due date
        if ($trip->invoice_date && $trip->credit_days) {
            $trip->due_date = $trip->invoice_date->addDays($trip->credit_days);
        }
    }

    /**
     * Check if quantity-related fields changed
     */
    private function quantityFieldsChanged(Trip $trip): bool
    {
        $quantityFields = [
            'quantity_loaded',
            'quantity_offloaded',
            'loading_first_weight',
            'loading_second_weight',
            'offloading_first_weight',
            'offloading_second_weight'
        ];

        foreach ($quantityFields as $field) {
            if ($trip->wasChanged($field)) {
                return true;
            }
        }

        return false;
    }
}
