<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shortages', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('trip_id')->constrained()->cascadeOnDelete();
            $table->foreignId('client_id')->constrained()->cascadeOnDelete();
            
            // Shortage Details
            $table->string('shortage_no')->unique();
            $table->enum('type', ['quantity_shortage', 'weight_shortage', 'quality_issue', 'damage'])
                  ->default('quantity_shortage');
            
            // Quantity Analysis
            $table->decimal('expected_quantity', 10, 2);
            $table->decimal('actual_quantity', 10, 2);
            $table->decimal('shortage_quantity', 10, 2); // Auto-calculated
            $table->decimal('shortage_percentage', 5, 2); // Auto-calculated
            
            // Weight Analysis
            $table->decimal('expected_weight', 10, 2)->nullable();
            $table->decimal('actual_weight', 10, 2)->nullable();
            $table->decimal('weight_difference', 10, 2)->nullable(); // Auto-calculated
            
            // Financial Impact
            $table->decimal('unit_rate', 8, 2);
            $table->decimal('shortage_value', 12, 2); // Auto-calculated
            $table->decimal('deduction_amount', 12, 2); // Auto-calculated
            $table->decimal('penalty_percentage', 5, 2)->default(0);
            $table->decimal('penalty_amount', 12, 2)->default(0); // Auto-calculated
            $table->decimal('total_deduction', 12, 2); // Auto-calculated
            
            // Investigation Details
            $table->enum('cause', [
                'loading_error', 'transport_loss', 'offloading_error', 
                'measurement_error', 'theft', 'evaporation', 'spillage', 'unknown'
            ])->nullable();
            $table->enum('responsibility', ['driver', 'client', 'loading_facility', 'transport_company', 'disputed'])
                  ->nullable();
            
            // Status and Resolution
            $table->enum('status', ['identified', 'investigating', 'disputed', 'accepted', 'resolved'])
                  ->default('identified');
            $table->date('identified_date');
            $table->date('investigation_date')->nullable();
            $table->date('resolution_date')->nullable();
            
            // Documentation and Evidence
            $table->text('description');
            $table->text('investigation_notes')->nullable();
            $table->text('resolution_notes')->nullable();
            $table->json('evidence_documents')->nullable(); // File paths
            $table->json('weight_tickets')->nullable(); // Weight ticket files
            
            // Client Communication
            $table->boolean('client_notified')->default(false);
            $table->date('client_notification_date')->nullable();
            $table->text('client_response')->nullable();
            $table->boolean('client_accepts_deduction')->nullable();
            
            // Auto-generation flags
            $table->boolean('auto_generated')->default(false);
            $table->json('calculation_details')->nullable(); // Store calculation breakdown
            $table->decimal('tolerance_threshold', 5, 2)->nullable(); // Threshold used for auto-generation
            
            // Audit Fields
            $table->foreignId('identified_by')->constrained('users');
            $table->foreignId('investigated_by')->nullable()->constrained('users');
            $table->foreignId('resolved_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Indexes
            $table->index(['trip_id', 'type']);
            $table->index(['client_id', 'identified_date']);
            $table->index(['status', 'identified_date']);
            $table->index('shortage_no');
            $table->index(['shortage_percentage', 'shortage_value']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shortages');
    }
};
