# Filament Resources Documentation - Service Management Module

## Overview

This document outlines the comprehensive Filament v4.x resources created for the fleet management service module. All resources follow the project's established patterns and conventions.

## Created Resources

### 1. ServiceTypeResource
**Location:** `app/Filament/Resources/ServiceTypes/ServiceTypeResource.php`

**Purpose:** Manage different types of services (Oil Change, Brake Service, etc.)

**Features:**
- **Form Components:**
  - Name and unique code fields
  - Service category selection (preventive, corrective, inspection)
  - Default intervals (KM and months)
  - Estimated cost with currency prefix
  - Required skills and tools (TagsInput)
  - Safety notes
  - Active/inactive toggle

- **Table Display:**
  - Essential columns: name, code, category, estimated cost, active status
  - Badge colors for categories (success, warning, info)
  - Service count relationship
  - Toggleable columns for intervals

- **Filters:**
  - Category filter
  - Active/inactive status filter

- **Navigation:**
  - Group: "Service Management"
  - Icon: Cog6Tooth
  - Sort order: 1
  - Badge: Total count

### 2. ServiceProviderResource
**Location:** `app/Filament/Resources/ServiceProviders/ServiceProviderResource.php`

**Purpose:** Manage external service providers and vendors

**Features:**
- **Form Sections:**
  - Basic Information (name, registration, tax ID, contact person)
  - Contact Information (email, phone, mobile, website)
  - Address (full address fields with country selection)
  - Service Information (specialties, certifications)
  - Additional Information (active status, notes)

- **Table Display:**
  - Essential columns: name, contact person, phone, city, certified status
  - Badge display for service specialties
  - Toggleable columns for detailed information
  - Service count relationship

- **Filters:**
  - Active/inactive status
  - Certified/not certified

- **Navigation:**
  - Group: "Service Management"
  - Icon: BuildingOffice2
  - Sort order: 2
  - Badge: Total count

### 3. ServiceResource (Main Resource)
**Location:** `app/Filament/Resources/Services/ServiceResource.php`

**Purpose:** Core service management with comprehensive CRUD operations

**Features:**
- **Form Sections:**
  - Service Information (name, description, type, category, priority)
  - Assignment (vehicle, technician, service provider, created by)
  - Scheduling (scheduled and actual start/end times)
  - Odometer & Financial (readings, costs, payments)
  - Status & Documentation (status, payment status, notes)

- **Table Display:**
  - Essential columns: name, vehicle, service type, status, priority, scheduled start, total cost, payment status
  - Color-coded badges for status and priority
  - Toggleable columns for additional details
  - Money formatting for costs

- **Advanced Filters:**
  - Status filter (5 options)
  - Priority filter (4 levels)
  - Service category filter
  - Payment status filter
  - Overdue services filter (custom query scope)

- **Relationships:**
  - Vehicle (searchable select)
  - Service Type (searchable select)
  - Service Provider (optional, searchable select)
  - Created By (auto-populated with current user)

- **Navigation:**
  - Group: "Service Management"
  - Icon: Wrench
  - Sort order: 3
  - Badge: Total count

### 4. ServicePartResource
**Location:** `app/Filament/Resources/ServiceParts/ServicePartResource.php`

**Purpose:** Track parts used in services

**Features:**
- **Form Sections:**
  - Part Information (service, part number, name, description, manufacturer, supplier)
  - Quantity & Cost (quantity, unit cost, total cost, condition)
  - Warranty Information (coverage, dates, terms)
  - Additional Information (serial/batch numbers, installation notes)

- **Table Display:**
  - Essential columns: service, part number, part name, supplier, quantity, costs, condition, warranty
  - Color-coded badges for part condition
  - Money formatting for costs
  - Warranty status icons

- **Filters:**
  - Part condition filter
  - Warranty status filter

- **Navigation:**
  - Group: "Service Management"
  - Icon: Cog8Tooth
  - Sort order: 4
  - Badge: Total count

## Page Structure

### Resource Pages Created:

1. **ServiceTypes:**
   - `ManageServiceTypes` (single page for CRUD)

2. **ServiceProviders:**
   - `ListServiceProviders`
   - `CreateServiceProvider`
   - `EditServiceProvider`

3. **Services:**
   - `ListServices`
   - `CreateService` (with auto-populated created_by)
   - `EditService`

4. **ServiceParts:**
   - `ManageServiceParts` (single page for CRUD)

## Key Features Implemented

### 1. Form Validation
- Required field validation
- Unique constraints (service type codes)
- Numeric validation for costs and quantities
- Email validation for service providers
- URL validation for websites

### 2. Relationship Management
- Searchable select components for all relationships
- Preloaded options for better performance
- Proper foreign key handling

### 3. User Experience
- Sectioned forms for better organization
- Appropriate column layouts (1-3 columns)
- Helper text for complex fields
- Default values where appropriate
- Auto-population of user context

### 4. Data Display
- Color-coded badges for status fields
- Money formatting for financial fields
- Date/datetime formatting
- Toggleable columns to reduce clutter
- Relationship display (e.g., vehicle.name)

### 5. Filtering & Search
- Multiple filter options per resource
- Searchable columns
- Custom query scopes (overdue services)
- Sortable columns

### 6. Navigation & Organization
- Logical grouping under "Service Management"
- Appropriate icons for each resource
- Sort order for logical flow
- Navigation badges showing record counts

## Integration with Existing Project

### Follows Project Patterns:
- Uses same namespace structure as existing resources
- Follows established import patterns
- Uses consistent page organization
- Maintains navigation grouping conventions

### Compatible with Existing Models:
- Leverages all model relationships
- Uses proper casts and accessors
- Integrates with existing scopes
- Respects model fillable/guarded properties

## Usage Instructions

### 1. Migration Setup
```bash
# Run migrations in correct order
php artisan migrate

# Seed service types
php artisan db:seed --class=ServiceTypeSeeder
```

### 2. Access Resources
- Navigate to admin panel
- Find "Service Management" group in sidebar
- Access each resource for CRUD operations

### 3. Workflow
1. Set up Service Types first
2. Add Service Providers as needed
3. Create Services with proper assignments
4. Add Service Parts to track components used

## Future Enhancements

### Potential Additions:
- Service scheduling calendar view
- Bulk service creation
- Service templates
- Advanced reporting widgets
- Service history timeline
- Mobile-responsive optimizations
- Export functionality
- Advanced search capabilities

This implementation provides a solid foundation for fleet service management while maintaining flexibility for future enhancements and customizations.
