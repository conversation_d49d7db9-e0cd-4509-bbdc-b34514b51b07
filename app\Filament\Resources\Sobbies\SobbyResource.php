<?php

namespace App\Filament\Resources\Sobbies;

use App\Filament\Resources\Sobbies\Pages\CreateSobby;
use App\Filament\Resources\Sobbies\Pages\EditSobby;
use App\Filament\Resources\Sobbies\Pages\ListSobbies;
use App\Models\Sobby;
use Filament\Resources\Resource;
use Filament\Forms\Schema;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Heroicon\Outline\CurrencyDollarIcon as Heroicon;

class SobbyResource extends Resource
{
    protected static ?string $model = Sobby::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::class;
    protected static string|UnitEnum|null $navigationGroup = 'Trip Management';
    protected static ?int $navigationSort = 2;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Sobby Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('sobby_no')
                                    ->label('Sobby Number')
                                    ->required()
                                    ->unique(ignoreRecord: true),
                                Select::make('trip_id')
                                    ->label('Trip')
                                    ->relationship('trip', 'trip_no')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Select::make('driver_id')
                                    ->label('Driver')
                                    ->relationship('driver', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Select::make('type')
                                    ->options([
                                        'fuel_allowance' => 'Fuel Allowance',
                                        'meal_allowance' => 'Meal Allowance',
                                        'accommodation' => 'Accommodation',
                                        'miscellaneous' => 'Miscellaneous',
                                    ])
                                    ->required(),
                                Select::make('status')
                                    ->options([
                                        'pending' => 'Pending',
                                        'approved' => 'Approved',
                                        'paid' => 'Paid',
                                        'cancelled' => 'Cancelled',
                                    ])
                                    ->default('pending')
                                    ->required(),
                                DatePicker::make('issue_date')
                                    ->required()
                                    ->default(now()),
                            ]),
                    ]),

                Section::make('Financial Details')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('amount')
                                    ->numeric()
                                    ->step(0.01)
                                    ->required(),
                                Select::make('currency')
                                    ->options([
                                        'USD' => 'USD',
                                        'NGN' => 'NGN',
                                        'EUR' => 'EUR',
                                        'GBP' => 'GBP',
                                    ])
                                    ->default('USD')
                                    ->required(),
                                TextInput::make('exchange_rate')
                                    ->numeric()
                                    ->step(0.0001)
                                    ->default(1.0000),
                            ]),
                    ]),

                Section::make('Calculation Details')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('trip_distance_km')
                                    ->label('Trip Distance (KM)')
                                    ->numeric()
                                    ->step(0.01),
                                TextInput::make('rate_per_km')
                                    ->label('Rate per KM')
                                    ->numeric()
                                    ->step(0.0001),
                                TextInput::make('trip_duration_days')
                                    ->label('Trip Duration (Days)')
                                    ->numeric(),
                                TextInput::make('daily_allowance_rate')
                                    ->label('Daily Allowance Rate')
                                    ->numeric()
                                    ->step(0.01),
                            ]),
                    ]),

                Section::make('Payment Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                DatePicker::make('payment_date'),
                                TextInput::make('payment_method'),
                                TextInput::make('payment_reference'),
                            ]),
                    ]),

                Section::make('Additional Information')
                    ->schema([
                        Textarea::make('description')
                            ->rows(3),
                        Textarea::make('notes')
                            ->rows(3),
                        Toggle::make('auto_generated')
                            ->label('Auto Generated')
                            ->disabled(),
                    ]),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('sobby_no')
                    ->label('Sobby No')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('trip.trip_no')
                    ->label('Trip')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('driver.name')
                    ->label('Driver')
                    ->searchable()
                    ->sortable(),
                
                BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'fuel_allowance',
                        'success' => 'meal_allowance',
                        'warning' => 'accommodation',
                        'secondary' => 'miscellaneous',
                    ]),
                
                TextColumn::make('amount')
                    ->money('USD')
                    ->sortable(),
                
                BadgeColumn::make('status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'approved',
                        'primary' => 'paid',
                        'danger' => 'cancelled',
                    ])
                    ->sortable(),
                
                TextColumn::make('issue_date')
                    ->date()
                    ->sortable(),
                
                TextColumn::make('payment_date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'paid' => 'Paid',
                        'cancelled' => 'Cancelled',
                    ])
                    ->multiple(),
                
                SelectFilter::make('type')
                    ->options([
                        'fuel_allowance' => 'Fuel Allowance',
                        'meal_allowance' => 'Meal Allowance',
                        'accommodation' => 'Accommodation',
                        'miscellaneous' => 'Miscellaneous',
                    ])
                    ->multiple(),
                
                SelectFilter::make('driver_id')
                    ->label('Driver')
                    ->relationship('driver', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->defaultSort('issue_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSobbies::route('/'),
            'create' => CreateSobby::route('/create'),
            'edit' => EditSobby::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::pending()->count();
    }
}
